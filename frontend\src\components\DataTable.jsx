import { useState, useEffect, useRef } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Box,
  Typography,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  CircularProgress,
  Skeleton,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import AddIcon from '@mui/icons-material/Add';
import SaveIcon from '@mui/icons-material/Save';

const DataTable = ({
  title,
  columns,
  data,
  onAdd,
  onEdit,
  onDelete,
  onSave,
  readOnly = false,
  addButtonText = "Ajouter une ligne",
  loading = false
}) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentItem, setCurrentItem] = useState({});
  const [editingId, setEditingId] = useState(null);
  const tableContainerRef = useRef(null);

  const handleOpenDialog = (item = {}, edit = false) => {
    setCurrentItem(item);
    setEditMode(edit);
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setCurrentItem({});
  };

  const scrollToBottom = () => {
    if (tableContainerRef.current) {
      setTimeout(() => {
        tableContainerRef.current.scrollTo({
          top: tableContainerRef.current.scrollHeight,
          behavior: 'smooth'
        });
      }, 100); // Small delay to ensure the new row is rendered
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCurrentItem({ ...currentItem, [name]: value });
  };

  const handleSaveItem = () => {
    // Make sure we have an ID for new items
    if (!editMode && !currentItem.id) {
      currentItem.id = Date.now().toString();
    }

    const isAdding = !editMode;

    // Call the appropriate function provided by the parent component
    // This will update the data in memory but not save to localStorage
    // The actual save to localStorage will happen when the "Sauvegarder les changements" button is clicked
    if (editMode) {
      onEdit(currentItem);
    } else {
      onAdd(currentItem);
    }
    handleCloseDialog();

    // Scroll to bottom if we just added a new item
    if (isAdding) {
      scrollToBottom();
    }
  };

  const handleEditRow = (item) => {
    setEditingId(item.id);
    setCurrentItem({ ...item });
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setCurrentItem({});
  };

  const handleSaveEdit = () => {
    // Call the onEdit function provided by the parent component
    // This will update the data in memory but not save to localStorage
    onEdit(currentItem);
    setEditingId(null);
    setCurrentItem({});
  };

  const renderCellContent = (item, column) => {
    if (editingId === item.id) {
      if (column.type === 'select' && column.options) {
        return (
          <FormControl fullWidth size="small">
            <Select
              name={column.field}
              value={currentItem[column.field] || ''}
              onChange={handleInputChange}
            >
              {column.options.map((option) => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );
      } else {
        return (
          <TextField
            name={column.field}
            value={currentItem[column.field] || ''}
            onChange={handleInputChange}
            type={column.type || 'text'}
            size="small"
            fullWidth
          />
        );
      }
    }

    // Handle valueGetter function (for custom column value calculation)
    if (column.valueGetter && typeof column.valueGetter === 'function') {
      try {
        return column.valueGetter({ row: item });
      } catch (error) {
        return item[column.field] || '';
      }
    }

    if (column.type === 'date' && item[column.field]) {
      try {
        // Handle different date formats
        let dateValue = item[column.field];
        let date;

        // Check if it's already in ISO format (YYYY-MM-DD)
        if (dateValue.match(/^\d{4}-\d{2}-\d{2}$/)) {
          date = new Date(dateValue);
        }
        // Check if it's in Excel serial number format
        else if (!isNaN(dateValue) && typeof dateValue === 'number') {
          // Excel dates are number of days since 1900-01-01
          // JavaScript dates are milliseconds since 1970-01-01
          const excelEpoch = new Date(1900, 0, 1);
          date = new Date(excelEpoch.getTime() + (dateValue - 1) * 24 * 60 * 60 * 1000);
        }
        // Try to parse as a date string
        else {
          date = new Date(dateValue);
        }

        // Format as DD/MM/YYYY
        if (isNaN(date.getTime())) {
          // If date is invalid, return the original value
          return dateValue;
        }

        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();

        return `${day}/${month}/${year}`;
      } catch (error) {
        return item[column.field]; // Return original value if there's an error
      }
    }

    return item[column.field];
  };

  return (
    <div className="table-container">
      {title && (
        <Box sx={{
          mb: 3,
          px: { xs: 1, sm: 2 },
          pt: 2
        }}>
          <Typography
            variant="h5"
            component="h2"
            sx={{
              fontWeight: 600,
              fontSize: { xs: '1.25rem', sm: '1.5rem' },
              textAlign: { xs: 'center', sm: 'left' }
            }}
          >
            {title}
          </Typography>
        </Box>
      )}

      <TableContainer
        ref={tableContainerRef}
        sx={{
          mb: 3,
          width: '100%',
          maxHeight: { xs: '60vh', sm: '70vh' }, // Responsive max height
          overflowX: 'auto',
          overflowY: 'auto', // Enable vertical scrolling
          position: 'relative',
          border: '1px solid rgba(99, 102, 241, 0.1)',
          borderRadius: '8px',
          boxSizing: 'border-box',
          '&::-webkit-scrollbar': {
            width: { xs: '6px', sm: '8px' },
            height: { xs: '6px', sm: '8px' },
          },
          '&::-webkit-scrollbar-thumb': {
            backgroundColor: 'rgba(99, 102, 241, 0.6)',
            borderRadius: '4px',
            '&:hover': {
              backgroundColor: 'rgba(99, 102, 241, 0.8)',
            }
          },
          '&::-webkit-scrollbar-track': {
            backgroundColor: 'rgba(15, 23, 42, 0.3)',
            borderRadius: '4px',
          },
          '&::-webkit-scrollbar-corner': {
            backgroundColor: 'rgba(15, 23, 42, 0.3)',
          }
        }}
      >
        <Table sx={{
          width: 'auto',
          minWidth: '100%',
          tableLayout: 'auto' // Changed from 'fixed' to 'auto' for better column sizing
        }}>
          <TableHead>
            <TableRow>
              {columns.map((column) => (
                <TableCell
                  key={column.field}
                  sx={{
                    minWidth: { xs: column.width ? `${parseInt(column.width.replace('px', '')) * 0.8}px` : '120px', sm: column.width || '150px' },
                    width: column.width || 'auto',
                    whiteSpace: 'normal', // Allow text wrapping in headers
                    overflow: 'visible', // Make sure header text is fully visible
                    padding: { xs: '8px 12px', sm: '12px 16px' },
                    backgroundColor: 'rgba(15, 23, 42, 0.95)',
                    backdropFilter: 'blur(8px)',
                    position: 'sticky',
                    top: 0,
                    zIndex: 10, // Higher z-index to ensure it stays on top
                    fontWeight: 600,
                    borderBottom: '2px solid rgba(99, 102, 241, 0.3)',
                    fontSize: { xs: '0.8rem', sm: '0.9rem' },
                    lineHeight: 1.4,
                    maxWidth: column.width || 'auto',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)', // Add shadow for better separation
                    boxSizing: 'border-box',
                  }}
                >
                  {column.headerName}
                </TableCell>
              ))}
              {!readOnly && (
                <TableCell
                  sx={{
                    width: { xs: '100px', sm: '120px' },
                    minWidth: { xs: '100px', sm: '120px' },
                    backgroundColor: 'rgba(15, 23, 42, 0.95)',
                    backdropFilter: 'blur(8px)',
                    position: 'sticky',
                    top: 0,
                    zIndex: 10, // Higher z-index to ensure it stays on top
                    fontWeight: 600,
                    borderBottom: '2px solid rgba(99, 102, 241, 0.3)',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)', // Add shadow for better separation
                    padding: { xs: '8px 12px', sm: '12px 16px' },
                    fontSize: { xs: '0.8rem', sm: '0.9rem' },
                    boxSizing: 'border-box',
                  }}
                >
                  Actions
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {loading && data.length === 0 ? (
              // Loading skeleton rows only when no data is available
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={`skeleton-${index}`}>
                  {columns.map((column) => (
                    <TableCell key={`skeleton-${index}-${column.field}`}>
                      <Skeleton variant="text" width="100%" height={24} />
                    </TableCell>
                  ))}
                  {!readOnly && (
                    <TableCell>
                      <Skeleton variant="text" width="100%" height={24} />
                    </TableCell>
                  )}
                </TableRow>
              ))
            ) : data.length > 0 ? (
              data.map((item) => (
                <TableRow key={item.id}>
                  {columns.map((column) => (
                    <TableCell
                      key={`${item.id}-${column.field}`}
                      sx={{
                        minWidth: { xs: column.width ? `${parseInt(column.width.replace('px', '')) * 0.8}px` : '120px', sm: column.width || '150px' },
                        width: column.width || 'auto',
                        whiteSpace: column.field === 'machinesPossibles' || column.field === 'composant' ? 'normal' : 'nowrap',
                        overflow: column.field === 'machinesPossibles' || column.field === 'composant' ? 'visible' : 'hidden',
                        textOverflow: column.field === 'machinesPossibles' || column.field === 'composant' ? 'unset' : 'ellipsis',
                        padding: { xs: '6px 12px', sm: '8px 16px' },
                        borderBottom: '1px solid rgba(99, 102, 241, 0.1)',
                        maxWidth: column.width || 'auto',
                        wordWrap: 'break-word',
                        lineHeight: column.field === 'machinesPossibles' || column.field === 'composant' ? '1.4' : '1.2',
                        fontSize: { xs: '0.8rem', sm: '0.875rem' },
                        boxSizing: 'border-box',
                      }}
                    >
                      {renderCellContent(item, column)}
                    </TableCell>
                  ))}
                  {!readOnly && (
                    <TableCell
                      sx={{
                        width: { xs: '100px', sm: '120px' },
                        minWidth: { xs: '100px', sm: '120px' },
                        padding: { xs: '6px 8px', sm: '8px 16px' },
                        borderBottom: '1px solid rgba(99, 102, 241, 0.1)',
                        boxSizing: 'border-box',
                      }}
                    >
                      {editingId === item.id ? (
                        <>
                          <Button
                            color="primary"
                            size="small"
                            onClick={handleSaveEdit}
                            sx={{
                              mr: 1,
                              fontSize: { xs: '0.7rem', sm: '0.8rem' },
                              padding: { xs: '4px 8px', sm: '6px 12px' }
                            }}
                          >
                            Save
                          </Button>
                          <Button
                            color="secondary"
                            size="small"
                            onClick={handleCancelEdit}
                            sx={{
                              fontSize: { xs: '0.7rem', sm: '0.8rem' },
                              padding: { xs: '4px 8px', sm: '6px 12px' }
                            }}
                          >
                            Cancel
                          </Button>
                        </>
                      ) : (
                        <>
                          <IconButton
                            color="primary"
                            size="small"
                            onClick={() => handleEditRow(item)}
                            sx={{
                              mr: 1,
                              padding: { xs: '4px', sm: '8px' }
                            }}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                          {onDelete && (
                            <IconButton
                              color="error"
                              size="small"
                              onClick={() => onDelete(item.id)}
                              sx={{
                                padding: { xs: '4px', sm: '8px' }
                              }}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          )}
                        </>
                      )}
                    </TableCell>
                  )}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length + (!readOnly ? 1 : 0)} align="center">
                  Aucune donnée disponible
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Table buttons container - holds both Add and Save buttons */}
      {!readOnly && (
        <div className="table-buttons-container">
          {/* Left space - empty */}
          <div className="button-left-space">
            {/* Empty space for balance */}
          </div>

          {/* Center - Add button */}
          <div className="button-center">
            {onAdd && (
              <Button
                variant="contained"
                color="primary"
                startIcon={<AddIcon />}
                onClick={() => handleOpenDialog()}
                sx={{
                  py: { xs: 1, sm: 1.2 },
                  px: { xs: 2, sm: 3 },
                  borderRadius: 2,
                  boxShadow: '0 4px 6px rgba(99, 102, 241, 0.2)',
                  fontSize: { xs: '0.8rem', sm: '0.875rem' },
                  '&:hover': {
                    boxShadow: '0 6px 8px rgba(99, 102, 241, 0.4)',
                    transform: 'translateY(-2px)',
                  },
                  transition: 'all 0.2s ease',
                }}
              >
                {addButtonText}
              </Button>
            )}
          </div>

          {/* Right - Save button */}
          <div className="button-right">
            {onSave ? (
              <Button
                variant="contained"
                color="primary"
                startIcon={<SaveIcon />}
                onClick={() => {
                  // Add a small delay to ensure the button click is registered
                  setTimeout(() => {
                    try {
                      onSave();
                    } catch (error) {
                      // Even if there's an error, we'll handle it in the save function
                      // and show appropriate notifications there
                    }
                  }, 100);
                }}
                sx={{
                  py: { xs: 1, sm: 1.2 },
                  px: { xs: 2, sm: 3 },
                  borderRadius: 2,
                  boxShadow: '0 4px 6px rgba(58, 134, 255, 0.2)',
                  fontSize: { xs: '0.8rem', sm: '0.875rem' },
                  '&:hover': {
                    boxShadow: '0 6px 8px rgba(58, 134, 255, 0.4)',
                    transform: 'translateY(-2px)',
                  },
                  transition: 'all 0.2s ease',
                }}
              >
                Sauvegarder les modifications
              </Button>
            ) : (
              <div style={{ width: '1px', height: '1px' }}></div> /* Empty placeholder when no save button */
            )}
          </div>
        </div>
      )}

      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="sm"
        fullWidth
        sx={{
          '& .MuiDialog-paper': {
            backgroundColor: 'rgba(15, 23, 42, 0.95)',
            backdropFilter: 'blur(8px)',
            borderRadius: 2,
            border: '1px solid rgba(99, 102, 241, 0.1)',
            margin: { xs: 1, sm: 2 },
            maxHeight: { xs: '90vh', sm: '80vh' },
            width: { xs: 'calc(100% - 16px)', sm: 'auto' },
          }
        }}
      >
        <DialogTitle>{editMode ? 'Modifier' : 'Ajouter'} un élément</DialogTitle>
        <DialogContent>
          {columns.map((column) => (
            <Box key={column.field} sx={{ mb: 2, mt: 1 }}>
              {column.type === 'select' && column.options ? (
                <FormControl fullWidth>
                  <InputLabel id={`${column.field}-label`}>{column.headerName}</InputLabel>
                  <Select
                    labelId={`${column.field}-label`}
                    name={column.field}
                    value={currentItem[column.field] || ''}
                    onChange={handleInputChange}
                    label={column.headerName}
                  >
                    {column.options.map((option) => (
                      <MenuItem key={option} value={option}>
                        {option}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              ) : (
                <TextField
                  fullWidth
                  label={column.headerName}
                  name={column.field}
                  value={currentItem[column.field] || ''}
                  onChange={handleInputChange}
                  type={column.type || 'text'}
                  sx={{ '& .MuiInputLabel-root': {
                    transform: column.type === 'date' ? 'translate(14px, -9px) scale(0.75)' : undefined
                  } }}
                />
              )}
            </Box>
          ))}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="secondary">
            Annuler
          </Button>
          <Button onClick={handleSaveItem} color="primary">
            {editMode ? 'Modifier' : 'Ajouter'}
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default DataTable;
