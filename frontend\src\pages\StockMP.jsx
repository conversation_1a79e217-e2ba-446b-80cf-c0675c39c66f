import React, { useState, useEffect } from 'react';
import { Container, Typography, Alert, Snackbar, Box, Paper, Divider, Button } from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import DataTable from '../components/DataTable';
import UnsavedChangesDialog from '../components/UnsavedChangesDialog';
import BackButton from '../components/BackButton';
import { stockMP, saveAllData, checkUnsavedChanges, discardChanges } from '../services/navbarApi';

const StockMP = () => {
  const [stockMPData, setStockMPData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);

  const columns = [
    { field: 'codeProduit', headerName: 'Code Produit', type: 'string', readOnly: true },
    { field: 'composant', headerName: 'Composant', type: 'string', readOnly: true },
    { field: 'designation', headerName: 'Désignation', type: 'string', readOnly: true },
    { field: 'quantiteEnStock', headerName: 'Quantité en Stock', type: 'number' },
  ];

  useEffect(() => {
    fetchData();
  }, []);

  // Add beforeunload event listener to show browser's native "unsaved changes" alert
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (hasUnsavedChanges()) {
        event.preventDefault();
        event.returnValue = ''; // This triggers the browser's native dialog
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Fetch Stock MP data (which is automatically synchronized with Ressources Matérielles)
      const stockData = await stockMP.getAll();

      setStockMPData(stockData);
      setError('');
    } catch (error) {
      setError('Erreur lors du chargement des données de stock MP');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = async () => {
    try {
      // For Stock MP, we don't manually add items. Instead, we synchronize with Ressources Matérielles
      // This will automatically add any new items from Ressources Matérielles
      await stockMP.add({}); // This triggers synchronization on the backend

      // Refresh the data to show the synchronized items
      await fetchData();

      showNotification('Stock MP synchronisé avec les Ressources Matérielles', 'success');
    } catch (error) {
      showNotification('Erreur lors de la synchronisation du stock MP', 'error');
    }
  };

  const handleEdit = async (updatedItem) => {
    try {
      // Convert numeric fields
      updatedItem.quantiteEnStock = parseFloat(updatedItem.quantiteEnStock) || 0;

      const editedItem = await stockMP.update(updatedItem);
      setStockMPData(stockMPData.map(item => item.id === editedItem.id ? editedItem : item));
      showNotification('Stock MP mis à jour avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la mise à jour du stock MP', 'error');
    }
  };

  const handleSave = async () => {
    try {
      // Save all data to backend
      const result = await saveAllData();

      // Always show success message since we've modified saveAllData to always return success: true
      // when the data is saved to localStorage, even if there were backend errors
      showNotification(result.message || 'Changements sauvegardés avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la sauvegarde des changements', 'error');
    }
  };

  // Handle dialog confirmation (OK button)
  const handleConfirmNavigation = () => {
    setShowUnsavedDialog(false);
    discardChanges();
    if (pendingNavigation) {
      window.location.href = pendingNavigation;
    }
  };

  // Handle dialog cancellation
  const handleCancelNavigation = () => {
    setShowUnsavedDialog(false);
    setPendingNavigation(null);
  };

  const showNotification = (message, severity) => {
    setNotification({ open: true, message, severity });
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };



  return (
    <Container sx={{ backgroundColor: 'transparent', maxWidth: '100%', padding: 0 }}>
      <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
        <BackButton />
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper elevation={3} sx={{ p: 2, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="h2">
            Stock MP
          </Typography>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Group data by Code Produit and display each group */}
        {Array.from(new Set(stockMPData.map(item => item.codeProduit)))
          .sort() // Sort product codes alphabetically
          .map(codeProduit => {
            // Get data for this product
            const productData = stockMPData.filter(item => item.codeProduit === codeProduit);

            return (
              <Box
                key={codeProduit}
                sx={{
                  mb: 4,
                  borderRadius: '8px',
                  overflow: 'hidden',
                  boxShadow: '0 3px 10px rgba(0, 0, 0, 0.1)'
                }}
              >
                {/* Product title */}
                <Box
                  sx={{
                    p: 2,
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                    display: 'flex',
                    alignItems: 'center'
                  }}
                >
                  <Typography
                    variant="h6"
                    component="h3"
                    sx={{
                      fontWeight: 'bold',
                      color: 'primary.main'
                    }}
                  >
                    Produit: {codeProduit}
                  </Typography>
                </Box>

                {/* Table for this product */}
                <Box sx={{ p: 0 }}>
                  <DataTable
                    title=""
                    columns={columns}
                    data={productData}
                    onAdd={null} // Remove add/synchronize functionality
                    onEdit={handleEdit}
                    onDelete={null} // Disable delete functionality
                    onSave={null} // Remove save button from individual tables
                    loading={loading}
                  />
                </Box>
              </Box>
            );
          })
        }

        {/* Show message if no data */}
        {stockMPData.length === 0 && !loading && (
          <Typography variant="body1" align="center" sx={{ my: 4 }}>
            Aucune donnée de stock MP disponible.
          </Typography>
        )}

        {/* Save button at the end of all tables */}
        {stockMPData.length > 0 && (
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<SaveIcon />}
              onClick={handleSave}
              sx={{
                py: 1.2,
                px: 3,
                borderRadius: 2,
                boxShadow: '0 4px 6px rgba(58, 134, 255, 0.2)',
                '&:hover': {
                  boxShadow: '0 6px 8px rgba(58, 134, 255, 0.4)',
                  transform: 'translateY(-2px)',
                },
                transition: 'all 0.2s ease',
              }}
            >
              Sauvegarder les modifications
            </Button>
          </Box>
        )}
      </Paper>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity}>
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Unsaved Changes Dialog */}
      <UnsavedChangesDialog
        open={showUnsavedDialog}
        onCancel={handleCancelNavigation}
        onConfirm={handleConfirmNavigation}
      />
    </Container>
  );
};

export default StockMP;
