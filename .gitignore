# Node.js dependencies
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
package-lock.json
yarn.lock

# Build files
/dist
/build
/out
/.next
/.nuxt
/.vuepress/dist

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.idea/
.vscode/
*.sublime-project
*.sublime-workspace
.project
.classpath
.settings/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Temporary files
*.tmp
*.temp
.cache/
.temp/

# Backend uploads
backend/uploads/

# Frontend build
frontend/dist/
frontend/build/

# Coverage directory
coverage/
.nyc_output/

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local
