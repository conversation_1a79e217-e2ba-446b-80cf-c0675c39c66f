import React, { useState, useEffect } from 'react';
import { Container, Typography, Alert, Snackbar, Box, Paper, Divider } from '@mui/material';
import DataTable from '../components/DataTable';
import UnsavedChangesDialog from '../components/UnsavedChangesDialog';
import BackButton from '../components/BackButton';
import { transit, saveAllData, checkUnsavedChanges, discardChanges } from '../services/navbarApi';

const Transit = () => {
  const [transitData, setTransitData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);

  const columns = [
    { field: 'machineSource', headerName: 'Machine Source', type: 'string' },
    { field: 'machineDestination', headerName: 'Machine Destination', type: 'string' },
    { field: 'tempsTransit', headerName: 'Temps Transit (min)', type: 'number' },
  ];

  useEffect(() => {
    fetchTransit(false); // Use cached data if available for faster loading
  }, []);

  // Add beforeunload event listener to show browser's native "unsaved changes" alert
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (hasUnsavedChanges()) {
        event.preventDefault();
        event.returnValue = ''; // This triggers the browser's native dialog
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const fetchTransit = async (forceRefresh = false) => {
    try {
      setLoading(true);
      const data = await transit.getAll(forceRefresh); // Only force refresh when explicitly requested
      setTransitData(data);
      setError('');
    } catch (error) {
      setError('Erreur lors du chargement des données de transit');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = async (newItem) => {
    try {
      // Add an ID if not present
      if (!newItem.id) {
        newItem.id = Date.now().toString();
      }

      // Convert numeric fields
      newItem.tempsTransit = parseInt(newItem.tempsTransit);

      const addedItem = await transit.add(newItem);
      // Refresh data from the API to ensure we have the latest state
      const updatedData = await transit.getAll();
      setTransitData(updatedData);
      showNotification('Transit ajouté avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de l\'ajout du transit', 'error');
    }
  };

  const handleEdit = async (updatedItem) => {
    try {
      // Convert numeric fields
      updatedItem.tempsTransit = parseInt(updatedItem.tempsTransit);

      const editedItem = await transit.update(updatedItem);
      setTransitData(transitData.map(item => item.id === editedItem.id ? editedItem : item));
      showNotification('Transit mis à jour avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la mise à jour du transit', 'error');
      console.error(error);
    }
  };

  const handleDelete = async (id) => {
    try {
      await transit.delete(id);
      setTransitData(transitData.filter(item => item.id !== id));
      showNotification('Transit supprimé avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la suppression du transit', 'error');
    }
  };

  const handleSave = async () => {
    try {
      // Save all data to backend
      const result = await saveAllData();

      // Always show success message since we've modified saveAllData to always return success: true
      // when the data is saved to localStorage, even if there were backend errors
      showNotification(result.message || 'Changements sauvegardés avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la sauvegarde des changements', 'error');
    }
  };

  // Handle navigation with unsaved changes
  const handleNavigation = (path) => {
    if (hasUnsavedChanges()) {
      setPendingNavigation(path);
      setShowUnsavedDialog(true);
    } else {
      window.location.href = path;
    }
  };

  // Handle dialog confirmation (OK button)
  const handleConfirmNavigation = () => {
    setShowUnsavedDialog(false);
    discardChanges();
    if (pendingNavigation) {
      window.location.href = pendingNavigation;
    }
  };

  // Handle dialog cancellation
  const handleCancelNavigation = () => {
    setShowUnsavedDialog(false);
    setPendingNavigation(null);
  };

  const showNotification = (message, severity) => {
    setNotification({ open: true, message, severity });
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  return (
    <Container sx={{ backgroundColor: 'transparent', maxWidth: '100%', padding: 0 }}>
      <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
        <BackButton />
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper elevation={3} sx={{ p: 2, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="h2">
            Transit
          </Typography>
        </Box>

        <Divider sx={{ mb: 2 }} />

        <DataTable
          title=""
          columns={columns}
          data={transitData}
          onAdd={handleAdd}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onSave={handleSave}
          loading={loading}
        />
      </Paper>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity}>
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Unsaved Changes Dialog */}
      <UnsavedChangesDialog
        open={showUnsavedDialog}
        onCancel={handleCancelNavigation}
        onConfirm={handleConfirmNavigation}
      />
    </Container>
  );
};

export default Transit;
