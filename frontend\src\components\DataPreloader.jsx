import { useEffect } from 'react';
import { preloadAllNavbarData } from '../services/navbarApi';

/**
 * DataPreloader component that preloads all navbar table data in the background
 * This component should be mounted early in the app lifecycle to improve performance
 */
const DataPreloader = () => {
  useEffect(() => {
    // Start preloading data immediately when component mounts
    const startPreloading = async () => {
      try {
        await preloadAllNavbarData();
      } catch (error) {
        // Silently fail - preloading is a performance optimization, not critical
      }
    };

    // Use a small delay to avoid blocking the initial render
    const timeoutId = setTimeout(startPreloading, 100);

    return () => clearTimeout(timeoutId);
  }, []);

  // This component doesn't render anything
  return null;
};

export default DataPreloader;
