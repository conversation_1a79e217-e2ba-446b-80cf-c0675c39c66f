import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Alert,
  Snackbar,
  Button,
  Box,
  Paper,
  Divider,
  CircularProgress,
  FormControlLabel,
  Switch
} from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import PrintIcon from '@mui/icons-material/Print';
import DataTable from '../components/DataTable';
import UnsavedChangesDialog from '../components/UnsavedChangesDialog';
import { getProduits, generatePlanning, addProduit, updateProduit, deleteProduit } from '../services/api';
import { formatDate, ensureWorkingHours } from '../utils/dateUtils';
import logoImage from '../assets/images/logo.png';

// Chart components (using a simple custom vertical bar chart implementation)
const SimpleBarChart = ({ data, title }) => {
  const chartHeight = 300;

  return (
    <Box sx={{ mt: 3, p: 3, backgroundColor: 'rgba(15, 23, 42, 0.05)', borderRadius: 2 }}>
      <Typography variant="h6" sx={{ mb: 3, textAlign: 'center', fontWeight: 600 }}>
        {title}
      </Typography>

      {/* Chart container */}
      <Box sx={{
        display: 'flex',
        alignItems: 'flex-end',
        justifyContent: 'center',
        gap: 2,
        height: chartHeight,
        px: 2,
        position: 'relative'
      }}>
        {/* Y-axis labels */}
        <Box sx={{
          position: 'absolute',
          left: 0,
          height: '100%',
          display: 'flex',
          flexDirection: 'column-reverse',
          justifyContent: 'space-between',
          py: 1
        }}>
          {[0, 25, 50, 75, 100].map(value => (
            <Typography key={value} variant="caption" sx={{ fontSize: '0.7rem', color: 'text.secondary' }}>
              {value}%
            </Typography>
          ))}
        </Box>

        {/* Grid lines */}
        <Box sx={{
          position: 'absolute',
          left: 30,
          right: 0,
          height: '100%',
          display: 'flex',
          flexDirection: 'column-reverse',
          justifyContent: 'space-between',
          py: 1
        }}>
          {[0, 25, 50, 75, 100].map(value => (
            <Box key={value} sx={{
              height: '1px',
              backgroundColor: 'rgba(0, 0, 0, 0.1)',
              width: '100%'
            }} />
          ))}
        </Box>

        {/* Bars */}
        <Box sx={{
          display: 'flex',
          alignItems: 'flex-end',
          gap: 2,
          height: '100%',
          ml: 4,
          flex: 1,
          justifyContent: 'flex-start', // Left align instead of center
          position: 'relative',
          py: 1 // Match grid padding
        }}>
          {data.map((item, index) => {
            // Calculate bar height to align with grid lines
            const gridHeight = chartHeight - 16; // Match grid area exactly
            const barHeight = (parseFloat(item.value) / 100) * gridHeight;

            return (
              <Box key={index} sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                minWidth: '60px',
                maxWidth: '80px',
                flex: 1,
                height: gridHeight, // Use exact grid height
                justifyContent: 'flex-end', // Align to bottom
                position: 'relative'
              }}>
                {/* Value label positioned above each bar individually */}
                <Typography
                  variant="caption"
                  sx={{
                    fontWeight: 600,
                    fontSize: '0.75rem',
                    color: 'text.primary',
                    position: 'absolute',
                    bottom: `${barHeight + 5}px`, // Position above the specific bar
                    left: '50%',
                    transform: 'translateX(-50%)',
                    zIndex: 10
                  }}
                >
                  {item.value}%
                </Typography>

                {/* Bar aligned with grid - starts from 0% */}
                <Box
                  sx={{
                    width: '100%',
                    height: `${barHeight}px`,
                    backgroundColor: `hsl(${220 + (index * 40) % 140}, 70%, 55%)`,
                    borderRadius: '4px 4px 0 0',
                    transition: 'all 0.3s ease',
                    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                    '&:hover': {
                      transform: 'scaleY(1.05)',
                      boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
                    }
                  }}
                />


              </Box>
            );
          })}
        </Box>

      </Box>

      {/* Name labels below the chart */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 2,
        ml: 4,
        flex: 1,
        justifyContent: 'flex-start',
        px: 2,
        mt: 2 // Add margin top to separate from chart
      }}>
        {data.map((item, index) => (
          <Box key={index} sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minWidth: '60px',
            maxWidth: '80px',
            flex: 1
          }}>
            <Typography
              variant="caption"
              sx={{
                textAlign: 'center',
                fontSize: '0.7rem',
                color: 'text.secondary',
                wordBreak: 'break-word',
                lineHeight: 1.2,
                maxWidth: '100%'
              }}
            >
              {item.name}
            </Typography>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

const PlanningResults = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // CRITICAL FIX: Use sessionStorage to persist entryMethod across page refreshes
  const getEntryMethod = () => {
    // First try to get from location.state (fresh navigation)
    if (location.state?.entryMethod) {
      return location.state.entryMethod;
    }
    // Then try sessionStorage (page refresh)
    const storedEntryMethod = sessionStorage.getItem('previousEntryMethod');
    if (storedEntryMethod) {
      return storedEntryMethod;
    }
    // Default fallback
    return 'excel';
  };

  const entryMethod = getEntryMethod();

  // CRITICAL FIX: Use sessionStorage flag to detect page refreshes
  // Set a flag when navigating, clear it on page refresh
  const [isPageRefresh, setIsPageRefresh] = useState(false);

  useEffect(() => {
    const wasNavigated = sessionStorage.getItem('wasNavigated');
    if (wasNavigated === 'true') {
      // This is a page refresh - clear the flag and set refresh state
      sessionStorage.removeItem('wasNavigated');
      setIsPageRefresh(true);
    } else {
      // This is a fresh navigation - set the flag
      sessionStorage.setItem('wasNavigated', 'true');
      setIsPageRefresh(false);
    }
  }, []);

  const clearData = location.state?.clearData || false; // Clear data flag from Home navigation
  const fromHome = location.state?.fromHome || false; // Flag to indicate navigation from Home

  // On page refresh, always set clearData and fromHome to false to preserve data
  const actualClearData = isPageRefresh ? false : clearData;
  const actualFromHome = isPageRefresh ? false : fromHome;

  const [produits, setProduits] = useState([]);
  const [productionPlanning, setProductionPlanning] = useState([]);
  const [planningMaterielles, setPlanningMaterielles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [generatingPlanning, setGeneratingPlanning] = useState(false);
  const [error, setError] = useState('');
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  const [activeTab, setActiveTab] = useState('produits');
  const [hasLocalChanges, setHasLocalChanges] = useState(false);
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);
  const [isRamadanHours, setIsRamadanHours] = useState(false);
  const [chargesMachines, setChargesMachines] = useState([]);
  const [chargesRH, setChargesRH] = useState([]);

  const produitsColumns = [
    { field: 'codeProduit', headerName: 'Code Produit', type: 'string' },
    { field: 'quantiteDemandee', headerName: 'Quantité Demandée', type: 'number' },
    { field: 'deadline', headerName: 'Deadline', type: 'date' },
    { field: 'priorite', headerName: 'Priorité', type: 'select', options: ['Faible', 'Moyenne', 'Haute'] },
    { field: 'quantiteeOuverte', headerName: 'Quantitée Ouverte', type: 'number' },
    { field: 'startDate', headerName: 'Start Date', type: 'date' },
    { field: 'tempsCycleTotal', headerName: 'Temps Cycle Total (min)', type: 'number' },
  ];

  const planningColumns = [
    { field: 'produit', headerName: 'Produit', type: 'string' },
    { field: 'poste', headerName: 'Poste', type: 'string' },
    { field: 'machine', headerName: 'Machine', type: 'string' },
    { field: 'operateur', headerName: 'Opérateur', type: 'string' },
    {
      field: 'debutFormatted',
      headerName: 'Début',
      type: 'string',
      valueGetter: (params) => {
        if (params.row.debutFormatted) return params.row.debutFormatted;
        if (params.row.debut) {
          const date = ensureWorkingHours(new Date(params.row.debut));
          return formatDate(date);
        }
        return '';
      }
    },
    {
      field: 'finFormatted',
      headerName: 'Fin',
      type: 'string',
      valueGetter: (params) => {
        if (params.row.finFormatted) return params.row.finFormatted;
        if (params.row.fin) {
          const date = ensureWorkingHours(new Date(params.row.fin));
          return formatDate(date);
        }
        return '';
      }
    },
    {
      field: 'tempsTotal',
      headerName: 'Temps Total (min)',
      type: 'number',
      description: 'Quantité Demandée × Temps Cycle + Temps Setup',
      valueFormatter: (params) => {
        return typeof params.value === 'number' ? params.value.toFixed(0) : params.value;
      },
      renderCell: (params) => {
        const tooltip = params.row.tempsCalcul ||
          `Total: ${params.value ? params.value.toFixed(0) : params.value} min (Quantité Demandée × Temps Cycle + Temps Setup)`;

        return (
          <div title={tooltip}>
            {typeof params.value === 'number' ? params.value.toFixed(0) : params.value}
          </div>
        );
      }
    },
    // Optional: Add these columns if you want to show the detailed breakdown
    // Uncomment if you want to display these columns
    /*
    {
      field: 'tempsSetup',
      headerName: 'Setup (min)',
      type: 'number',
      valueFormatter: (params) => params.value?.toFixed(1) || '0.0'
    },
    {
      field: 'tempsCyclePar',
      headerName: 'Cycle/unité (min)',
      type: 'number',
      valueFormatter: (params) => params.value?.toFixed(1) || '0.0'
    },
    {
      field: 'quantite',
      headerName: 'Quantité',
      type: 'number'
    },
    {
      field: 'tempsTransit',
      headerName: 'Transit (min)',
      type: 'number',
      valueFormatter: (params) => params.value?.toFixed(1) || '0.0'
    },
    */
  ];

  const planningMateriellesColumns = [
    { field: 'codeProduit', headerName: 'Code Produit', type: 'string' },
    { field: 'composant', headerName: 'Composant', type: 'number' },
    { field: 'designation', headerName: 'Designation', type: 'string' },
    {
      field: 'besoinsbruts',
      headerName: 'Besoins Bruts',
      type: 'number',
      valueFormatter: (params) => {
        return typeof params.value === 'number' ? params.value.toFixed(3) : params.value;
      }
    },
    {
      field: 'stockProjecte',
      headerName: 'Stock Projeté',
      type: 'number',
      valueFormatter: (params) => {
        return typeof params.value === 'number' ? params.value.toFixed(3) : params.value;
      }
    },
    { field: 'dateCommandeSuggeree', headerName: 'Date de Commande Suggérée', type: 'string' },
    {
      field: 'qteACommander',
      headerName: 'Qté à Commander',
      type: 'number',
      valueFormatter: (params) => {
        return typeof params.value === 'number' ? params.value.toFixed(3) : params.value;
      }
    },
  ];

  const chargesMachinesColumns = [
    { field: 'machine', headerName: 'Machine', type: 'string' },
    { field: 'poste', headerName: 'Poste', type: 'string' },
    { field: 'charge', headerName: 'Charge', type: 'string' },
    {
      field: 'chargeTotal',
      headerName: 'Charge Total',
      type: 'number',
      valueFormatter: (params) => {
        return typeof params.value === 'number' ? `${params.value.toFixed(0)} min` : params.value;
      }
    },
  ];

  const chargesRHColumns = [
    { field: 'operateur', headerName: 'Opérateur', type: 'string' },
    { field: 'poste', headerName: 'Poste', type: 'string' },
    { field: 'charge', headerName: 'Charge', type: 'string' },
    {
      field: 'chargeTotal',
      headerName: 'Charge Total',
      type: 'number',
      valueFormatter: (params) => {
        return typeof params.value === 'number' ? `${params.value.toFixed(0)} min` : params.value;
      }
    },
  ];

  useEffect(() => {
    fetchProductsOnly();
  }, [entryMethod, actualClearData, actualFromHome]);

  const fetchProductsOnly = async () => {
    try {
      setLoading(true);

      // CRITICAL FIX: Only clear data when explicitly coming from Home with clearData=true
      // For page refreshes or direct navigation, never clear data (persist manual products)
      const shouldClearData = actualClearData === true && actualFromHome === true;

      // Debug logging
      console.log('🔍 FETCH DEBUG - entryMethod:', entryMethod);
      console.log('🔍 FETCH DEBUG - isPageRefresh:', isPageRefresh);
      console.log('🔍 FETCH DEBUG - clearData:', clearData, 'actualClearData:', actualClearData);
      console.log('🔍 FETCH DEBUG - fromHome:', fromHome, 'actualFromHome:', actualFromHome);
      console.log('🔍 FETCH DEBUG - shouldClearData:', shouldClearData);
      console.log('🔍 FETCH DEBUG - localStorage before fetch:', localStorage.getItem('planificationMichaudData'));

      const produitsData = await getProduits(entryMethod, shouldClearData);
      console.log('🔍 FETCH DEBUG - loaded produits data:', produitsData);
      setProduits(produitsData);

      // Clear planning data - it should only be generated when user clicks "Générer le Planning"
      setProductionPlanning([]);
      setPlanningMaterielles([]);

      setError('');
    } catch (error) {
      console.error('🔍 FETCH DEBUG - error:', error);
      setError('Erreur lors du chargement des données');
    } finally {
      setLoading(false);
    }
  };

  // Navigation handling functions
  const handleNavigation = (path) => {
    if (hasLocalChanges) {
      setPendingNavigation(path);
      setShowUnsavedDialog(true);
    } else {
      navigate(path);
    }
  };

  const handleCancelNavigation = () => {
    setShowUnsavedDialog(false);
    setPendingNavigation(null);
  };

  const handleConfirmNavigation = () => {
    setShowUnsavedDialog(false);
    if (pendingNavigation) {
      navigate(pendingNavigation);
    }
  };

  // Add beforeunload event listener to warn when closing the window with unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasLocalChanges) {
        e.preventDefault();
        e.returnValue = 'Vous avez des changements non sauvegardés. Êtes-vous sûr de vouloir quitter?';
        return e.returnValue;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasLocalChanges]);

  const handleAddProduit = async (newItem) => {
    try {
      // Add an ID if not present
      if (!newItem.id) {
        newItem.id = Date.now().toString();
      }

      // Convert numeric fields
      newItem.quantiteDemandee = parseInt(newItem.quantiteDemandee);
      newItem.tempsCycleTotal = parseFloat(newItem.tempsCycleTotal);

      // Add the product
      const addedItem = await addProduit(newItem, entryMethod);

      // Update the state with the new product
      setProduits([...produits, addedItem]);

      // Mark that we have unsaved changes
      setHasLocalChanges(true);

      showNotification('Produit ajouté avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de l\'ajout du produit', 'error');
    }
  };

  const handleEditProduit = async (updatedItem) => {
    try {
      // Convert numeric fields
      updatedItem.quantiteDemandee = parseInt(updatedItem.quantiteDemandee);
      updatedItem.tempsCycleTotal = parseFloat(updatedItem.tempsCycleTotal);

      // Update the product
      const editedItem = await updateProduit(updatedItem, entryMethod);

      // Update the state with the edited product
      setProduits(produits.map(item => item.id === editedItem.id ? editedItem : item));

      // Mark that we have unsaved changes
      setHasLocalChanges(true);

      showNotification('Produit mis à jour avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la mise à jour du produit', 'error');
    }
  };

  const handleDeleteProduit = async (id) => {
    try {
      // Delete the product
      await deleteProduit(id, entryMethod);

      // Update the state by filtering out the deleted product
      setProduits(produits.filter(item => item.id !== id));

      // Mark that we have unsaved changes
      setHasLocalChanges(true);

      showNotification('Produit supprimé avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la suppression du produit', 'error');
    }
  };

  const handleSaveProduits = async () => {
    try {
      console.log('💾 SAVE DEBUG - entryMethod:', entryMethod, 'produits count:', produits.length);
      console.log('💾 SAVE DEBUG - produits data:', produits);

      // For manual products, ensure data is properly saved to localStorage
      // The data should already be saved through individual CRUD operations,
      // but let's make sure by re-saving the current state
      if (entryMethod === 'manual') {
        // Import the API functions
        const { clearProductsData, addProduit } = await import('../services/api');

        console.log('💾 SAVE DEBUG - clearing and re-saving manual products...');

        // Clear existing manual products and re-add current state
        clearProductsData('manual');

        // Re-add all current products to ensure they're properly saved
        for (const produit of produits) {
          console.log('💾 SAVE DEBUG - saving produit:', produit);
          await addProduit(produit, 'manual');
        }

        // Check localStorage after saving
        const savedData = localStorage.getItem('planificationMichaudData');
        console.log('💾 SAVE DEBUG - localStorage after save:', savedData);
      }

      // Mark as saved
      setHasLocalChanges(false);
      showNotification('Changements sauvegardés avec succès', 'success');
    } catch (error) {
      console.error('💾 SAVE DEBUG - error:', error);
      showNotification('Erreur lors de la sauvegarde des changements', 'error');
    }
  };

  // Helper function to get priority order (for sorting)
  const getPriorityOrder = (priority) => {
    if (!priority) return 3; // Default lowest priority if not specified

    const priorityLower = priority.toLowerCase();
    if (priorityLower.includes('haute')) return 1;
    if (priorityLower.includes('moyenne')) return 2;
    if (priorityLower.includes('faible')) return 3;
    return 3; // Default to lowest priority
  };

  // Helper function to get product priority from planning data
  const getProductPriority = (productCode) => {
    // Find the product in the produits array
    const product = produits.find(p => p.codeProduit === productCode);
    return product?.priorite || 'Faible'; // Default to 'Faible' if not found
  };

  const handleGeneratePlanning = async () => {
    try {
      setGeneratingPlanning(true);

      // Validate that we have produits data
      if (produits.length === 0) {
        throw new Error('Aucun produit disponible. Veuillez d\'abord ajouter des produits ou charger un fichier Excel.');
      }

      // Generate production planning with current entry method and working hours mode
      const planningData = await generatePlanning(entryMethod, isRamadanHours);

      if (planningData.length === 0) {
        showNotification('Planning généré mais aucune donnée produite. Vérifiez que les tables de navigation (Flux Produits, Machines, etc.) contiennent des données.', 'warning');
      }

      setProductionPlanning(planningData);

      // Generate planning materielles data
      const planningMateriellesData = await generatePlanningMaterielles();
      setPlanningMaterielles(planningMateriellesData);

      // Generate charges machines data using the planning data directly
      const chargesMachinesData = await generateChargesMachines(planningData);
      setChargesMachines(chargesMachinesData);

      // Generate charges RH data using the planning data directly
      const chargesRHData = await generateChargesRH(planningData);
      setChargesRH(chargesRHData);

      // Switch to planning tab after generating
      setActiveTab('planning');

      if (planningData.length > 0 || planningMateriellesData.length > 0) {
        showNotification('Planning généré avec succès', 'success');
      }
    } catch (error) {
      // Check if it's a deadline error for special handling
      if (error.message && error.message.includes('Veuillez prolonger le délai')) {
        // For deadline errors, show a more prominent error display and block generation
        setError(error.message);
        showNotification('Génération bloquée : Délais insuffisants détectés', 'error');
        // Clear any existing planning data to prevent confusion
        setProductionPlanning([]);
        setPlanningMaterielles([]);
      } else {
        // For other errors, use normal notification
        showNotification(error.message || 'Erreur lors de la génération du planning', 'error');
      }
    } finally {
      setGeneratingPlanning(false);
    }
  };

  // Function to generate Planning de Matérielles data locally
  const generatePlanningMaterielles = async () => {
    try {
      // Get the required navbar table data from backend
      const { getRessourcesMaterielles, getStockMP } = await import('../services/api');

      const [ressourcesMaterielles, stockMP] = await Promise.all([
        getRessourcesMaterielles(),
        getStockMP()
      ]);

      // Create Planning de Matérielles data using the same logic as backend
      const planningMaterielles = [];

      // Process each item in Ressources Matérielles
      ressourcesMaterielles.forEach(ressource => {
        // Find corresponding stock data
        const stockItem = stockMP.find(stock =>
          stock.codeProduit === ressource.codeProduit &&
          stock.composant === ressource.composant
        );

        // Find corresponding product data
        const produit = produits.find(p => p.codeProduit === ressource.codeProduit);

        if (produit) {
          const quantiteDemandee = parseInt(produit.quantiteDemandee) || 0;
          // Use qteBrut instead of quantiteParProduit for correct calculation
          const quantiteParProduit = parseFloat(ressource.qteBrut) || parseFloat(ressource.quantiteParProduit) || 0;
          const stockActuel = stockItem ? parseFloat(stockItem.quantiteEnStock) || 0 : 0;
          const seuil = parseFloat(ressource.seuil) || 0;
          const moq = parseFloat(ressource.moq) || 1;

          // Calculate besoins bruts: Quantité Demandée × Qté Brut
          const besoinsbruts = quantiteDemandee * quantiteParProduit;

          // Calculate stock projete: Stock Actuel - Besoins Bruts
          const stockProjecte = stockActuel - besoinsbruts;

          // Calculate qte a commander
          let qteACommander = 0;
          if (stockProjecte < seuil) {
            const deficit = seuil - stockProjecte;
            qteACommander = Math.ceil(deficit / moq) * moq;
          }

          // Calculate date commande suggeree: Start Date - Délai d'appro (jours calendaires)
          const delaiAppro = parseInt(ressource.delaiAppro) || 0;
          const startDate = produit.startDate ? new Date(produit.startDate) : new Date();
          const dateCommandeSuggeree = new Date(startDate);
          dateCommandeSuggeree.setDate(dateCommandeSuggeree.getDate() - delaiAppro);

          planningMaterielles.push({
            id: `${ressource.codeProduit}-${ressource.composant}-${Date.now()}`,
            codeProduit: ressource.codeProduit,
            composant: ressource.composant,
            designation: ressource.designation || 'N/A', // Include designation field
            quantiteParProduit: quantiteParProduit,
            besoinsbruts: besoinsbruts,
            stockActuel: stockActuel,
            stockProjecte: stockProjecte,
            seuil: seuil,
            moq: moq,
            qteACommander: qteACommander,
            dateCommandeSuggeree: dateCommandeSuggeree.toLocaleDateString('fr-FR')
          });
        }
      });

      return planningMaterielles;
    } catch (error) {
      return [];
    }
  };

  // Function to calculate working minutes between two dates (8:00-16:00, excluding weekends)
  const calculateWorkingMinutesBetweenDates = (startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    let totalWorkingMinutes = 0;

    // Working hours: 8:00 to 16:00 (8 hours = 480 minutes per day)
    const workStartHour = 8;
    const workEndHour = 16;

    let currentDate = new Date(start);
    currentDate.setHours(0, 0, 0, 0); // Start from beginning of day

    while (currentDate <= end) {
      // Skip weekends (Saturday = 6, Sunday = 0)
      const dayOfWeek = currentDate.getDay();
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        // This is a working day
        const dayStart = new Date(currentDate);
        dayStart.setHours(workStartHour, 0, 0, 0);

        const dayEnd = new Date(currentDate);
        dayEnd.setHours(workEndHour, 0, 0, 0);

        // Calculate overlap with the actual period
        const periodStart = start > dayStart ? start : dayStart;
        const periodEnd = end < dayEnd ? end : dayEnd;

        if (periodStart < periodEnd) {
          const minutesThisDay = (periodEnd - periodStart) / (1000 * 60);
          totalWorkingMinutes += minutesThisDay;
        }
      }

      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return totalWorkingMinutes;
  };

  // Function to generate Charges Machines data from Planning de Production
  const generateChargesMachines = async (planningData) => {
    try {
      // Get transit data to check for transit times
      const { transit } = await import('../services/api');
      const transitData = await transit.getAll();

      // Function to get transit time between two machines
      const getTransitTime = (sourceMachine, destinationMachine) => {
        if (!sourceMachine || !destinationMachine) return 0;

        const transitEntry = transitData.find(t => {
          const source = (t.machineSource || '').trim();
          const destination = (t.machineDestination || '').trim();
          return source === sourceMachine.trim() && destination === destinationMachine.trim();
        });

        return transitEntry ? (parseInt(transitEntry.tempsTransit) || 0) : 0;
      };

      // Group planning data by machine
      const machineGroups = {};

      planningData.forEach((planningItem, index) => {
        const machineName = planningItem.machine;
        if (!machineGroups[machineName]) {
          machineGroups[machineName] = {
            machine: machineName,
            poste: planningItem.poste,
            workSessions: []
          };
        }

        // Check if this step has transit time from previous machine
        let transitTimeUsed = 0;

        // Find the previous step for the same product to check for transit time
        const previousStepIndex = index - 1;
        if (previousStepIndex >= 0) {
          const previousStep = planningData[previousStepIndex];
          // Check if this is the next step of the same product
          if (previousStep.produit === planningItem.produit) {
            const previousMachine = previousStep.machine;
            const currentMachine = planningItem.machine;

            // Get transit time from Transit table
            transitTimeUsed = getTransitTime(previousMachine, currentMachine);
          }
        }

        machineGroups[machineName].workSessions.push({
          debut: new Date(planningItem.debut),
          fin: new Date(planningItem.fin),
          tempsTotal: planningItem.tempsTotal || 0,
          tempsSetup: planningItem.tempsSetup || 0,
          transitTimeUsed: transitTimeUsed,
          produit: planningItem.produit
        });
      });

      // Calculate charges for each machine
      const chargesMachinesData = [];

      Object.values(machineGroups).forEach(machineGroup => {
        const { machine, poste, workSessions } = machineGroup;

        // Note: We don't need machine setup time from machines table since
        // it's already included in the planning data as tempsSetup

        // Sort work sessions by start time
        workSessions.sort((a, b) => a.debut - b.debut);

        // Calculate total temps de charge for all sessions
        let totalTempsDeCharge = 0;

        workSessions.forEach(session => {
          // Temps de charge = Temps Total - Temps Setup - Temps Transit (if exists)
          // Note: Temps Total includes setup time and transit time (if any), so we subtract both
          let tempsDeCharge = session.tempsTotal - session.tempsSetup;

          // Subtract transit time if it exists for this step
          if (session.transitTimeUsed > 0) {
            tempsDeCharge -= session.transitTimeUsed;
          }

          // Ensure temps de charge is not negative
          tempsDeCharge = Math.max(0, tempsDeCharge);
          totalTempsDeCharge += tempsDeCharge;
        });

        // Calculate temps période based on number of work sessions
        let tempsPeriode;

        if (workSessions.length === 1) {
          // Single session: calculate working hours for just this session
          const session = workSessions[0];
          tempsPeriode = calculateWorkingMinutesBetweenDates(session.debut, session.fin);
        } else {
          // Multiple sessions: calculate working hours from first start to last end
          const firstDebut = workSessions[0].debut;
          const lastFin = workSessions[workSessions.length - 1].fin;
          tempsPeriode = calculateWorkingMinutesBetweenDates(firstDebut, lastFin);
        }

        // Calculate taux de charge percentage
        const tauxDeCharge = tempsPeriode > 0 ? (totalTempsDeCharge / tempsPeriode) * 100 : 0;

        chargesMachinesData.push({
          id: `charge-${machine}-${Date.now()}`,
          machine: machine,
          poste: poste,
          charge: `${tauxDeCharge.toFixed(1)}%`,
          chargeTotal: totalTempsDeCharge // Add Temps de charge in minutes
        });
      });

      return chargesMachinesData;
    } catch (error) {
      return [];
    }
  };

  // Function to generate Charges RH data from Planning de Production
  const generateChargesRH = async (planningData) => {
    try {
      // Get transit data to check for transit times
      const { transit } = await import('../services/api');
      const transitData = await transit.getAll();

      // Function to get transit time between two machines
      const getTransitTime = (sourceMachine, destinationMachine) => {
        if (!sourceMachine || !destinationMachine) return 0;

        const transitEntry = transitData.find(t => {
          const source = (t.machineSource || '').trim();
          const destination = (t.machineDestination || '').trim();
          return source === sourceMachine.trim() && destination === destinationMachine.trim();
        });

        return transitEntry ? (parseInt(transitEntry.tempsTransit) || 0) : 0;
      };

      // Group planning data by operator
      const operatorGroups = {};

      planningData.forEach((planningItem, index) => {
        const operatorName = planningItem.operateur || 'inconnu';
        if (!operatorGroups[operatorName]) {
          operatorGroups[operatorName] = {
            operateur: operatorName,
            poste: planningItem.poste || 'N/A',
            workSessions: []
          };
        }

        // Check if this step has transit time from previous machine
        let transitTimeUsed = 0;

        // Find the previous step for the same product to check for transit time
        const previousStepIndex = index - 1;
        if (previousStepIndex >= 0) {
          const previousStep = planningData[previousStepIndex];
          // Check if this is the next step of the same product
          if (previousStep.produit === planningItem.produit) {
            const previousMachine = previousStep.machine;
            const currentMachine = planningItem.machine;

            // Get transit time from Transit table
            transitTimeUsed = getTransitTime(previousMachine, currentMachine);
          }
        }

        operatorGroups[operatorName].workSessions.push({
          debut: new Date(planningItem.debut),
          fin: new Date(planningItem.fin),
          tempsTotal: planningItem.tempsTotal || 0,
          tempsSetup: planningItem.tempsSetup || 0,
          transitTimeUsed: transitTimeUsed,
          produit: planningItem.produit
        });
      });

      // Calculate charges for each operator
      const chargesRHData = [];

      Object.values(operatorGroups).forEach(operatorGroup => {
        const { operateur, poste, workSessions } = operatorGroup;

        // Sort work sessions by start time
        workSessions.sort((a, b) => a.debut - b.debut);

        // Calculate total temps de charge for all sessions
        let totalTempsDeCharge = 0;

        workSessions.forEach(session => {
          // Temps de charge = Temps Total - Temps Setup - Temps Transit (if exists)
          let tempsDeCharge = session.tempsTotal - session.tempsSetup;

          // Subtract transit time if it exists for this step
          if (session.transitTimeUsed > 0) {
            tempsDeCharge -= session.transitTimeUsed;
          }

          // Ensure temps de charge is not negative
          tempsDeCharge = Math.max(0, tempsDeCharge);
          totalTempsDeCharge += tempsDeCharge;
        });

        // Calculate temps période based on number of work sessions
        let tempsPeriode;

        if (workSessions.length === 1) {
          // Single session: calculate working hours for just this session
          const session = workSessions[0];
          tempsPeriode = calculateWorkingMinutesBetweenDates(session.debut, session.fin);
        } else {
          // Multiple sessions: calculate working hours from first start to last end
          const firstDebut = workSessions[0].debut;
          const lastFin = workSessions[workSessions.length - 1].fin;
          tempsPeriode = calculateWorkingMinutesBetweenDates(firstDebut, lastFin);
        }

        // Calculate taux de charge percentage
        const tauxDeCharge = tempsPeriode > 0 ? (totalTempsDeCharge / tempsPeriode) * 100 : 0;

        chargesRHData.push({
          id: `charge-rh-${operateur}-${Date.now()}`,
          operateur: operateur,
          poste: poste,
          charge: `${tauxDeCharge.toFixed(1)}%`,
          chargeTotal: totalTempsDeCharge // Add Temps de charge in minutes
        });
      });

      return chargesRHData;
    } catch (error) {
      return [];
    }
  };

  // Function to handle tab switching
  const switchToTab = (tab) => {
    setActiveTab(tab);
    // Clear error when switching tabs
    if (error) {
      setError('');
    }
  };

  const showNotification = (message, severity) => {
    setNotification({ open: true, message, severity });
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  // Convert image to base64 for better print compatibility
  const getImageAsBase64 = (imageSrc) => {
    return new Promise((resolve) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        const dataURL = canvas.toDataURL('image/png');
        resolve(dataURL);
      };
      img.onerror = () => {
        // Fallback to original src if conversion fails
        resolve(imageSrc);
      };
      img.src = imageSrc;
    });
  };

  // Create logo directly as HTML using the actual logo image
  const getLogoHTML = async () => {
    try {
      const base64Logo = await getImageAsBase64(logoImage);
      return `
        <img
          src="${base64Logo}"
          alt="Planification Michaud Logo"
          style="
            height: 25px;
            width: auto;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
          "
        />
      `;
    } catch (error) {
      // Fallback to direct image reference
      return `
        <img
          src="${logoImage}"
          alt="Planification Michaud Logo"
          style="
            height: 25px;
            width: auto;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
          "
        />
      `;
    }
  };

  // Print functions
  const handlePrintPlanningProduction = async () => {
    try {
      const logoHTML = await getLogoHTML();
      const printContent = generatePrintContent('Planning de Production', productionPlanning, planningColumns, produits, logoHTML);

      const printWindow = window.open('', 'PrintWindow', 'width=800,height=600');
      printWindow.document.open();
      printWindow.document.write(printContent);
      printWindow.document.close();

      setTimeout(() => {
        printWindow.focus();
        printWindow.print();
        printWindow.close();
      }, 500);
    } catch (error) {
      showNotification('Erreur lors de l\'impression', 'error');
    }
  };

  const handlePrintPlanningMaterielles = async () => {
    try {
      const logoHTML = await getLogoHTML();
      const printContent = generatePrintContent('Planning de Matérielles', planningMaterielles, planningMateriellesColumns, produits, logoHTML);

      const printWindow = window.open('', 'PrintWindow', 'width=800,height=600');
      printWindow.document.open();
      printWindow.document.write(printContent);
      printWindow.document.close();

      setTimeout(() => {
        printWindow.focus();
        printWindow.print();
        printWindow.close();
      }, 500);
    } catch (error) {
      showNotification('Erreur lors de l\'impression', 'error');
    }
  };

  const handlePrintChargesMachines = async () => {
    try {
      const logoHTML = await getLogoHTML();
      const printContent = generatePrintContent('Charges Machines', chargesMachines, chargesMachinesColumns, null, logoHTML);

      const printWindow = window.open('', 'PrintWindow', 'width=800,height=600');
      printWindow.document.open();
      printWindow.document.write(printContent);
      printWindow.document.close();

      setTimeout(() => {
        printWindow.focus();
        printWindow.print();
        printWindow.close();
      }, 500);
    } catch (error) {
      showNotification('Erreur lors de l\'impression', 'error');
    }
  };

  const handlePrintChargesRH = async () => {
    try {
      const logoHTML = await getLogoHTML();
      const printContent = generatePrintContent('Charges RH', chargesRH, chargesRHColumns, null, logoHTML);

      const printWindow = window.open('', 'PrintWindow', 'width=800,height=600');
      printWindow.document.open();
      printWindow.document.write(printContent);
      printWindow.document.close();

      setTimeout(() => {
        printWindow.focus();
        printWindow.print();
        printWindow.close();
      }, 500);
    } catch (error) {
      showNotification('Erreur lors de l\'impression', 'error');
    }
  };

  const generatePrintContent = (title, data, columns, produitsData = null, logoHTML = '') => {
    const currentDate = new Date();
    const formattedDate = currentDate.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
    const formattedTime = currentDate.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });

    let content = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${title}</title>
        <style>
          @media print {
            body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
            .print-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 30px;
              position: relative;
              min-height: 120px;
            }
            .print-logo {
              position: absolute;
              left: 0;
              top: 0;
            }
            .print-logo img {
              height: 25px !important;
              width: auto;
            }
            .print-date {
              position: absolute;
              right: 0;
              top: 0;
              font-size: 12px;
              color: #666;
              text-align: right;
              line-height: 1.4;
            }
            .print-title {
              font-size: 40px;
              font-weight: bold;
              margin: 0 auto;
              text-align: center;
              flex: 1;
              padding-top: 60px;
            }
            .print-table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 30px;
              page-break-inside: auto;
            }
            .print-table th, .print-table td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: left;
              font-size: 12px;
              page-break-inside: avoid;
            }
            .print-table th {
              background-color: #f5f5f5;
              font-weight: bold;
              page-break-after: avoid;
            }
            .print-table tbody tr {
              page-break-inside: avoid;
            }
            .product-section {
              margin-bottom: 40px;
              page-break-inside: auto;
              break-inside: auto;
            }
            .product-title {
              font-size: 18px;
              font-weight: bold;
              margin-bottom: 15px;
              padding: 10px;
              background-color: #f0f0f0;
              page-break-after: avoid;
            }
            .priority-haute { background-color: #ffebee; }
            .priority-moyenne { background-color: #e8f5e8; }
            .priority-faible { background-color: #e3f2fd; }
            @page {
              margin: 20px;
              @bottom-center {
                content: "Page " counter(page) " sur " counter(pages);
                font-size: 12px;
                color: #666;
              }
            }
          }
        </style>
      </head>
      <body>
        <div class="print-header">
          <div class="print-logo">${logoHTML}</div>
          <div class="print-date">
            Généré le ${formattedDate}<br>
            à ${formattedTime}
          </div>
          <div class="print-title">${title}</div>
        </div>
    `;

    if (title === 'Planning de Production' && produitsData) {
      // Group by products for Planning de Production
      const uniqueProducts = Array.from(new Set(data.map(item => item.produit)))
        .map(produit => ({
          code: produit,
          priority: getProductPriority(produit),
          priorityOrder: getPriorityOrder(getProductPriority(produit))
        }))
        .sort((a, b) => a.priorityOrder - b.priorityOrder);

      uniqueProducts.forEach(product => {
        const productData = data.filter(item => item.produit === product.code);
        const productDetails = produitsData.find(p => p.codeProduit === product.code);
        const quantity = productDetails?.quantiteDemandee || 'N/A';

        const priorityClass = product.priorityOrder === 1 ? 'priority-haute' :
                             product.priorityOrder === 2 ? 'priority-moyenne' : 'priority-faible';

        content += `
          <div class="product-section">
            <div class="product-title ${priorityClass}">
              Produit: ${product.code} | ${quantity} | Priorité: ${product.priority}
            </div>
            <table class="print-table">
              <thead>
                <tr>
                  ${columns.map(col => `<th>${col.headerName}</th>`).join('')}
                </tr>
              </thead>
              <tbody>
                ${productData.map(row => `
                  <tr>
                    ${columns.map(col => {
                      let value = row[col.field];
                      if (col.field === 'debutFormatted' && row.debutFormatted) value = row.debutFormatted;
                      else if (col.field === 'finFormatted' && row.finFormatted) value = row.finFormatted;
                      else if (col.field === 'tempsTotal' && typeof value === 'number') value = value.toFixed(0);
                      return `<td>${value || ''}</td>`;
                    }).join('')}
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        `;
      });
    } else if (title === 'Planning de Matérielles' && produitsData) {
      // Group by products for Planning de Matérielles (same structure as Planning de Production)
      const uniqueProducts = Array.from(new Set(data.map(item => item.codeProduit)))
        .map(codeProduit => ({
          code: codeProduit,
          priority: getProductPriority(codeProduit),
          priorityOrder: getPriorityOrder(getProductPriority(codeProduit))
        }))
        .sort((a, b) => a.priorityOrder - b.priorityOrder);

      uniqueProducts.forEach(product => {
        const productData = data.filter(item => item.codeProduit === product.code);
        const productDetails = produitsData.find(p => p.codeProduit === product.code);
        const quantity = productDetails?.quantiteDemandee || 'N/A';

        const priorityClass = product.priorityOrder === 1 ? 'priority-haute' :
                             product.priorityOrder === 2 ? 'priority-moyenne' : 'priority-faible';

        content += `
          <div class="product-section">
            <div class="product-title ${priorityClass}">
              Produit: ${product.code} | ${quantity} | Priorité: ${product.priority}
            </div>
            <table class="print-table">
              <thead>
                <tr>
                  ${columns.map(col => `<th>${col.headerName}</th>`).join('')}
                </tr>
              </thead>
              <tbody>
                ${productData.map(row => `
                  <tr>
                    ${columns.map(col => {
                      let value = row[col.field];
                      if ((col.field === 'besoinsbruts' || col.field === 'stockProjecte' || col.field === 'qteACommander') && typeof value === 'number') {
                        value = value.toFixed(3);
                      }
                      return `<td>${value || ''}</td>`;
                    }).join('')}
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        `;
      });
    } else {
      // Fallback: Single table for other cases (including Charges Machines)
      content += `
        <table class="print-table">
          <thead>
            <tr>
              ${columns.map(col => `<th>${col.headerName}</th>`).join('')}
            </tr>
          </thead>
          <tbody>
            ${data.map(row => `
              <tr>
                ${columns.map(col => {
                  let value = row[col.field];
                  if ((col.field === 'besoinsbruts' || col.field === 'stockProjecte' || col.field === 'qteACommander') && typeof value === 'number') {
                    value = value.toFixed(3);
                  }
                  // Note: charge field now already contains the '%' symbol in the data
                  return `<td>${value || ''}</td>`;
                }).join('')}
              </tr>
            `).join('')}
          </tbody>
        </table>
      `;
    }

    content += `
        </body>
      </html>
    `;

    return content;
  };

  return (
    <Container className="planning-container" sx={{ backgroundColor: 'transparent', maxWidth: '100%', padding: 0 }}>
      <Box sx={{ display: 'flex', flexDirection: 'column', mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
          <Button
            variant="outlined"
            color="primary"
            onClick={() => handleNavigation('/')}
            startIcon={<NavigateBeforeIcon />}
            sx={{
              borderRadius: '8px',
              padding: '8px 16px',
              minWidth: 'auto',
              '&:hover': {
                transform: 'translateY(-2px)',
                boxShadow: '0 4px 8px rgba(99, 102, 241, 0.2)',
              },
              transition: 'all 0.2s ease'
            }}
          >
            Retour à l'accueil
          </Button>
        </Box>

        <Typography variant="h4" component="h1" gutterBottom>
          Résultats de Planification
        </Typography>
      </Box>

      {error && (
        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
          <Alert
            severity="error"
            sx={{
              backgroundColor: '#ffebee',
              border: '2px solid #f44336',
              borderRadius: '12px',
              padding: '12px 24px',
              maxWidth: '500px',
              '& .MuiAlert-icon': {
                display: 'none'
              },
              '& .MuiAlert-message': {
                padding: 0,
                width: '100%'
              }
            }}
          >
          {error.includes('Veuillez prolonger le délai') ? (
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body1" sx={{ color: '#d32f2f', fontWeight: 600, mb: 1, fontSize: '16px' }}>
                🚫 Génération Bloquée - Délais Insuffisants
              </Typography>
              <Typography variant="body1" sx={{ color: '#d32f2f', fontWeight: 500, fontSize: '14px' }}>
                {error}
              </Typography>
            </Box>
          ) : (
            error
          )}
          </Alert>
        </Box>
      )}

      <Paper elevation={3} sx={{ p: 2, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          {/* Title section with navigation tabs for planning modes */}
          {activeTab === 'produits' ? (
            <Typography variant="h6" component="h2">
              Produits
            </Typography>
          ) : (
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant={activeTab === 'planning' ? 'contained' : 'outlined'}
                color="primary"
                onClick={() => switchToTab('planning')}
                sx={{
                  color: activeTab === 'planning' ? 'primary.light' : 'text.secondary',
                  backgroundColor: activeTab === 'planning' ? 'rgba(99, 102, 241, 0.15)' : 'transparent',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  fontWeight: activeTab === 'planning' ? 600 : 500,
                  position: 'relative',
                  overflow: 'hidden',
                  border: 'none',
                  '&:hover': {
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    color: 'primary.light',
                    transform: 'translateY(-2px)',
                    border: 'none',
                  },
                  '&::after': activeTab === 'planning' ? {
                    content: '""',
                    position: 'absolute',
                    bottom: 0,
                    left: '20%',
                    width: '60%',
                    height: '3px',
                    backgroundColor: 'primary.main',
                    borderRadius: '3px 3px 0 0',
                  } : {},
                  transition: 'all 0.2s ease-in-out',
                  mx: 0.5
                }}
              >
                Planning de Production
              </Button>
              <Button
                variant={activeTab === 'materielles' ? 'contained' : 'outlined'}
                color="primary"
                onClick={() => switchToTab('materielles')}
                sx={{
                  color: activeTab === 'materielles' ? 'primary.light' : 'text.secondary',
                  backgroundColor: activeTab === 'materielles' ? 'rgba(99, 102, 241, 0.15)' : 'transparent',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  fontWeight: activeTab === 'materielles' ? 600 : 500,
                  position: 'relative',
                  overflow: 'hidden',
                  border: 'none',
                  '&:hover': {
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    color: 'primary.light',
                    transform: 'translateY(-2px)',
                    border: 'none',
                  },
                  '&::after': activeTab === 'materielles' ? {
                    content: '""',
                    position: 'absolute',
                    bottom: 0,
                    left: '20%',
                    width: '60%',
                    height: '3px',
                    backgroundColor: 'primary.main',
                    borderRadius: '3px 3px 0 0',
                  } : {},
                  transition: 'all 0.2s ease-in-out',
                  mx: 0.5
                }}
              >
                Planning de Matérielles
              </Button>
              <Button
                variant={activeTab === 'charges' ? 'contained' : 'outlined'}
                color="primary"
                onClick={() => switchToTab('charges')}
                sx={{
                  color: activeTab === 'charges' ? 'primary.light' : 'text.secondary',
                  backgroundColor: activeTab === 'charges' ? 'rgba(99, 102, 241, 0.15)' : 'transparent',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  fontWeight: activeTab === 'charges' ? 600 : 500,
                  position: 'relative',
                  overflow: 'hidden',
                  border: 'none',
                  '&:hover': {
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    color: 'primary.light',
                    transform: 'translateY(-2px)',
                    border: 'none',
                  },
                  '&::after': activeTab === 'charges' ? {
                    content: '""',
                    position: 'absolute',
                    bottom: 0,
                    left: '20%',
                    width: '60%',
                    height: '3px',
                    backgroundColor: 'primary.main',
                    borderRadius: '3px 3px 0 0',
                  } : {},
                  transition: 'all 0.2s ease-in-out',
                  mx: 0.5
                }}
              >
                Charges Machines
              </Button>
              <Button
                variant={activeTab === 'chargesRH' ? 'contained' : 'outlined'}
                color="primary"
                onClick={() => switchToTab('chargesRH')}
                sx={{
                  color: activeTab === 'chargesRH' ? 'primary.light' : 'text.secondary',
                  backgroundColor: activeTab === 'chargesRH' ? 'rgba(99, 102, 241, 0.15)' : 'transparent',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  fontWeight: activeTab === 'chargesRH' ? 600 : 500,
                  position: 'relative',
                  overflow: 'hidden',
                  border: 'none',
                  '&:hover': {
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    color: 'primary.light',
                    transform: 'translateY(-2px)',
                    border: 'none',
                  },
                  '&::after': activeTab === 'chargesRH' ? {
                    content: '""',
                    position: 'absolute',
                    bottom: 0,
                    left: '20%',
                    width: '60%',
                    height: '3px',
                    backgroundColor: 'primary.main',
                    borderRadius: '3px 3px 0 0',
                  } : {},
                  transition: 'all 0.2s ease-in-out',
                  mx: 0.5
                }}
              >
                Charges RH
              </Button>
            </Box>
          )}

          {activeTab === 'produits' && (
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="contained"
                color="primary"
                endIcon={<NavigateNextIcon />}
                onClick={handleGeneratePlanning}
                disabled={loading || generatingPlanning || produits.length === 0}
              >
                {generatingPlanning ? <CircularProgress size={24} /> : 'Générer le Planning'}
              </Button>
            </Box>
          )}

          {/* Print buttons for planning tabs */}
          {activeTab === 'planning' && productionPlanning.length > 0 && (
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<PrintIcon />}
                onClick={handlePrintPlanningProduction}
                sx={{
                  borderRadius: '8px',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 8px rgba(99, 102, 241, 0.2)',
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                Imprimer
              </Button>
            </Box>
          )}

          {activeTab === 'materielles' && planningMaterielles.length > 0 && (
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<PrintIcon />}
                onClick={handlePrintPlanningMaterielles}
                sx={{
                  borderRadius: '8px',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 8px rgba(99, 102, 241, 0.2)',
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                Imprimer
              </Button>
            </Box>
          )}

          {activeTab === 'charges' && chargesMachines.length > 0 && (
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<PrintIcon />}
                onClick={handlePrintChargesMachines}
                sx={{
                  borderRadius: '8px',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 8px rgba(99, 102, 241, 0.2)',
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                Imprimer
              </Button>
            </Box>
          )}

          {activeTab === 'chargesRH' && chargesRH.length > 0 && (
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<PrintIcon />}
                onClick={handlePrintChargesRH}
                sx={{
                  borderRadius: '8px',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 8px rgba(99, 102, 241, 0.2)',
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                Imprimer
              </Button>
            </Box>
          )}


        </Box>

        <Divider sx={{ mb: 2 }} />

        {activeTab === 'produits' ? (
          <>
            {/* Working Hours Toggle */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={isRamadanHours}
                    onChange={(e) => setIsRamadanHours(e.target.checked)}
                    color="primary"
                  />
                }
                label={
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    {isRamadanHours ? 'Horaires Ramadan (8:30 - 13:30)' : 'Horaires Normaux (8:00 - 16:00)'}
                  </Typography>
                }
                sx={{
                  '& .MuiFormControlLabel-label': {
                    color: 'text.primary'
                  }
                }}
              />
            </Box>

            <DataTable
              title=""
              columns={produitsColumns}
              data={produits}
              onAdd={entryMethod === 'manual' ? handleAddProduit : undefined}
              onEdit={entryMethod === 'manual' ? handleEditProduit : undefined}
              onDelete={entryMethod === 'manual' ? handleDeleteProduit : undefined}
              onSave={entryMethod === 'manual' ? handleSaveProduits : undefined}
              readOnly={entryMethod === 'excel'}
              loading={loading}
            />
          </>
        ) : (
          <>
            {/* Add "Précédent" button */}
            <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
              <Button
                variant="outlined"
                color="primary"
                onClick={() => switchToTab('produits')}
                startIcon={<NavigateBeforeIcon />}
                sx={{
                  borderRadius: '8px',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 4px 8px rgba(99, 102, 241, 0.2)',
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                Précédent
              </Button>
            </Box>

            {/* Conditional rendering based on active tab */}
            {activeTab === 'planning' && (
              <>
                {/* Main title for Planning de Production */}
                <Box sx={{ mb: 3, px: 2, pt: 2 }}>
                  <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
                    Planning de Production
                  </Typography>
                </Box>

                {/* Get unique products and sort them by priority */}
                {Array.from(new Set(productionPlanning.map(item => item.produit)))
                  .map(produit => ({
                    code: produit,
                    priority: getProductPriority(produit),
                    priorityOrder: getPriorityOrder(getProductPriority(produit))
                  }))
                  .sort((a, b) => a.priorityOrder - b.priorityOrder) // Sort by priority (highest first)
                  .map(product => {
                    // Get planning data for this product
                    const productPlanning = productionPlanning.filter(item => item.produit === product.code);

                    return (
                      <Box
                        key={product.code}
                        sx={{
                          mb: 5,
                          borderRadius: '8px',
                          overflow: 'hidden',
                          boxShadow: '0 3px 10px rgba(0, 0, 0, 0.1)'
                        }}
                      >
                        {/* Product title with priority and quantity */}
                        <Box
                          sx={{
                            p: 2,
                            backgroundColor: product.priorityOrder === 1 ? '#c62828' :
                                             product.priorityOrder === 2 ? '#2e7d32' :
                                             '#1565c0',
                            borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between'
                          }}
                        >
                          <Typography
                            variant="h6"
                            component="h3"
                            sx={{
                              fontWeight: 'bold',
                              color: 'white',
                              textShadow: '0 1px 2px rgba(0, 0, 0, 0.2)'
                            }}
                          >
                            {/* Find the product in produits array to get the quantity */}
                            {(() => {
                              const productDetails = produits.find(p => p.codeProduit === product.code);
                              const quantity = productDetails?.quantiteDemandee || 'N/A';
                              return (
                                <>
                                  Produit: {product.code} <span style={{ marginLeft: '8px', marginRight: '8px' }}>|</span> {quantity}
                                </>
                              );
                            })()}
                          </Typography>
                          <Box
                            sx={{
                              fontSize: '0.8em',
                              backgroundColor: 'white',
                              color: product.priorityOrder === 1 ? '#c62828' :
                                     product.priorityOrder === 2 ? '#2e7d32' : '#1565c0',
                              padding: '4px 12px',
                              borderRadius: '20px',
                              fontWeight: 'bold',
                              display: 'flex',
                              alignItems: 'center',
                              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.2)'
                            }}
                          >
                            Priorité: {product.priority}
                          </Box>
                        </Box>

                        {/* Table for this product */}
                        <Box sx={{ p: 0 }}>
                          <DataTable
                            title=""
                            columns={planningColumns}
                            data={productPlanning}
                            readOnly={true}
                            loading={loading || generatingPlanning}
                          />
                        </Box>
                      </Box>
                    );
                  })
                }

                {/* Show message if no planning data */}
                {productionPlanning.length === 0 && !loading && !generatingPlanning && (
                  <Typography variant="body1" align="center" sx={{ my: 4 }}>
                    Aucune donnée de planning disponible. Veuillez générer un planning.
                  </Typography>
                )}

                {/* Show loading indicator */}
                {(loading || generatingPlanning) && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                    <CircularProgress />
                  </Box>
                )}
              </>
            )}

            {/* Planning de Matérielles content */}
            {activeTab === 'materielles' && (
              <>
                {/* Main title for Planning de Matérielles */}
                <Box sx={{ mb: 3, px: 2, pt: 2 }}>
                  <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
                    Planning de Matérielles
                  </Typography>
                </Box>

                {/* Get unique products and sort them by priority */}
                {Array.from(new Set(planningMaterielles.map(item => item.codeProduit)))
                  .map(codeProduit => ({
                    code: codeProduit,
                    priority: getProductPriority(codeProduit),
                    priorityOrder: getPriorityOrder(getProductPriority(codeProduit))
                  }))
                  .sort((a, b) => a.priorityOrder - b.priorityOrder) // Sort by priority (highest first)
                  .map(product => {
                    // Get planning data for this product
                    const productMateriellesPlanning = planningMaterielles.filter(item => item.codeProduit === product.code);

                    return (
                      <Box
                        key={product.code}
                        sx={{
                          mb: 5,
                          borderRadius: '8px',
                          overflow: 'hidden',
                          boxShadow: '0 3px 10px rgba(0, 0, 0, 0.1)'
                        }}
                      >
                        {/* Product title with priority and quantity */}
                        <Box
                          sx={{
                            p: 2,
                            backgroundColor: product.priorityOrder === 1 ? '#c62828' :
                                             product.priorityOrder === 2 ? '#2e7d32' :
                                             '#1565c0',
                            borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between'
                          }}
                        >
                          <Typography
                            variant="h6"
                            component="h3"
                            sx={{
                              fontWeight: 'bold',
                              color: 'white',
                              textShadow: '0 1px 2px rgba(0, 0, 0, 0.2)'
                            }}
                          >
                            {/* Find the product in produits array to get the quantity */}
                            {(() => {
                              const productDetails = produits.find(p => p.codeProduit === product.code);
                              const quantity = productDetails?.quantiteDemandee || 'N/A';
                              return (
                                <>
                                  Produit: {product.code} <span style={{ marginLeft: '8px', marginRight: '8px' }}>|</span> {quantity}
                                </>
                              );
                            })()}
                          </Typography>
                          <Box
                            sx={{
                              fontSize: '0.8em',
                              backgroundColor: 'white',
                              color: product.priorityOrder === 1 ? '#c62828' :
                                     product.priorityOrder === 2 ? '#2e7d32' : '#1565c0',
                              padding: '4px 12px',
                              borderRadius: '20px',
                              fontWeight: 'bold',
                              display: 'flex',
                              alignItems: 'center',
                              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.2)'
                            }}
                          >
                            Priorité: {product.priority}
                          </Box>
                        </Box>

                        {/* Table for this product */}
                        <Box sx={{ p: 0 }}>
                          <DataTable
                            title=""
                            columns={planningMateriellesColumns}
                            data={productMateriellesPlanning}
                            readOnly={true}
                            loading={loading}
                          />
                        </Box>
                      </Box>
                    );
                  })
                }

                {/* Show message if no planning materielles data */}
                {planningMaterielles.length === 0 && !loading && (
                  <Typography variant="body1" align="center" sx={{ my: 4 }}>
                    Aucune donnée de planning matérielles disponible.
                  </Typography>
                )}

                {/* Show loading indicator */}
                {loading && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                    <CircularProgress />
                  </Box>
                )}
              </>
            )}

            {/* Charges Machines content */}
            {activeTab === 'charges' && (
              <>
                {/* Main title for Charges Machines */}
                <Box sx={{ mb: 3, px: 2, pt: 2 }}>
                  <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
                    Charges Machines
                  </Typography>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <DataTable
                    title=""
                    columns={chargesMachinesColumns}
                    data={chargesMachines}
                    readOnly={true}
                    loading={loading || generatingPlanning}
                  />
                </Box>

                {/* Chart for Charges Machines */}
                {chargesMachines.length > 0 && (
                  <SimpleBarChart
                    data={chargesMachines.map(item => ({
                      name: item.machine,
                      value: parseFloat(item.charge.replace('%', ''))
                    }))}
                    title="Graphique des Charges Machines"
                  />
                )}

                {/* Show message if no charges machines data */}
                {chargesMachines.length === 0 && !loading && !generatingPlanning && (
                  <Typography variant="body1" align="center" sx={{ my: 4 }}>
                    Aucune donnée de charges machines disponible. Veuillez générer un planning.
                  </Typography>
                )}

                {/* Show loading indicator */}
                {(loading || generatingPlanning) && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                    <CircularProgress />
                  </Box>
                )}
              </>
            )}

            {/* Charges RH content */}
            {activeTab === 'chargesRH' && (
              <>
                {/* Main title for Charges RH */}
                <Box sx={{ mb: 3, px: 2, pt: 2 }}>
                  <Typography variant="h5" component="h2" sx={{ fontWeight: 600 }}>
                    Charges RH
                  </Typography>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <DataTable
                    title=""
                    columns={chargesRHColumns}
                    data={chargesRH}
                    readOnly={true}
                    loading={loading || generatingPlanning}
                  />
                </Box>

                {/* Chart for Charges RH */}
                {chargesRH.length > 0 && (
                  <SimpleBarChart
                    data={chargesRH.map(item => ({
                      name: item.operateur,
                      value: parseFloat(item.charge.replace('%', ''))
                    }))}
                    title="Graphique des Charges RH"
                  />
                )}

                {/* Show message if no charges RH data */}
                {chargesRH.length === 0 && !loading && !generatingPlanning && (
                  <Typography variant="body1" align="center" sx={{ my: 4 }}>
                    Aucune donnée de charges RH disponible. Veuillez générer un planning.
                  </Typography>
                )}

                {/* Show loading indicator */}
                {(loading || generatingPlanning) && (
                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                    <CircularProgress />
                  </Box>
                )}
              </>
            )}
          </>
        )}


      </Paper>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity}>
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Unsaved Changes Dialog */}
      <UnsavedChangesDialog
        open={showUnsavedDialog}
        onCancel={handleCancelNavigation}
        onConfirm={handleConfirmNavigation}
      />
    </Container>
  );
};

export default PlanningResults;
