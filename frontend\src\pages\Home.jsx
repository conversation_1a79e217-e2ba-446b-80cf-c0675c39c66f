import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Button,
  FormControl,
  Select,
  MenuItem,
  Paper,
  Grid,
  Input,
} from '@mui/material';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import EditIcon from '@mui/icons-material/Edit';
import { uploadExcel, clearProductsData } from '../services/api';

const Home = () => {
  const [inputMethod, setInputMethod] = useState('');
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  const handleInputMethodChange = (e) => {
    setInputMethod(e.target.value);
    setError('');
  };

  const handleFileChange = (e) => {
    if (e.target.files.length > 0) {
      setFile(e.target.files[0]);
      setError('');
    }
  };

  const handleFileUpload = async () => {
    if (!file) {
      setError('Veuillez sélectionner un fichier Excel');
      return;
    }

    // Validate file type
    const fileExtension = file.name.split('.').pop().toLowerCase();
    if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
      setError('Veuillez sélectionner un fichier Excel valide (.xlsx ou .xls)');
      return;
    }

    try {
      setLoading(true);

      // Check if the file has the correct sheet name
      // In a real implementation, we would use a library like xlsx to check this
      // For now, we'll just check the file name to simulate this validation
      if (!file.name.includes('Produits') && !file.name.toLowerCase().includes('produits')) {
        // We'll still process the file, but show a warning that it might not have the correct format
        console.warn('The Excel file might not have a sheet named "Produits"');
      }

      // Clear manual products data before uploading Excel file
      clearProductsData('manual');

      // Upload the Excel file and process it (this will replace existing Excel data)
      const result = await uploadExcel(file);

      // Navigate with state parameter to indicate Excel upload method
      navigate('/planning-results', { state: { entryMethod: 'excel', fromHome: true } });
    } catch (error) {

      // Extract the detailed error message if available
      let errorMessage = 'Erreur lors du traitement du fichier Excel';

      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', error.response.data);

        if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        } else {
          errorMessage += `: ${error.response.status} ${error.response.statusText}`;
        }
      } else if (error.request) {
        // The request was made but no response was received
        errorMessage = 'Aucune réponse du serveur. Veuillez vérifier votre connexion.';
      } else {
        // Something happened in setting up the request that triggered an Error
        errorMessage += ': ' + (error.message || 'Format de fichier incorrect ou données invalides');
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleManualEntry = () => {
    // Clear any existing products data before starting manual entry
    clearProductsData('excel');
    clearProductsData('manual');

    // Clear the navigation flag to ensure fresh navigation detection
    sessionStorage.removeItem('wasNavigated');

    // Navigate with state parameter to indicate manual entry method and clear data flag
    navigate('/planning-results', { state: { entryMethod: 'manual', clearData: true, fromHome: true } });
  };

  return (
    <Container maxWidth="md" className="home-container">
      <Paper
        elevation={0}
        sx={{
          p: { xs: 3, md: 5 },
          borderRadius: 3,
          background: 'rgba(3, 7, 18, 0.8)',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
          border: '1px solid rgba(99, 102, 241, 0.1)',
          overflow: 'hidden',
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '4px',
            background: 'linear-gradient(90deg, #6366f1, #3b82f6)',
          },
        }}
        className="hover-card"
      >
        <Typography
          variant="h3"
          component="h1"
          gutterBottom
          align="center"
          sx={{
            fontWeight: 700,
            background: 'linear-gradient(90deg, #6366f1, #3b82f6)',
            backgroundClip: 'text',
            textFillColor: 'transparent',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 4,
            letterSpacing: '-0.5px',
          }}
        >
          Planification Michaud
        </Typography>

        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: { xs: 'flex-start', sm: 'center' },
            mb: 5,
            mt: 4,
            gap: 2,
          }}
        >
          <Typography
            variant="body1"
            sx={{
              mr: { xs: 0, sm: 2 },
              fontWeight: 600,
              color: theme => theme.palette.text.primary,
            }}
          >
            Méthode de saisie :
          </Typography>
          <Box sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: 'center',
            width: { xs: '100%', sm: 'auto' },
            gap: { xs: 2, sm: 3 }  // Add gap between dropdown and button
          }}>
            <FormControl
              sx={{
                minWidth: 250, // Increased width to accommodate the text
                width: { xs: '100%', sm: 'auto' }, // Full width on mobile, auto on larger screens
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
                  },
                  '&.Mui-focused': {
                    boxShadow: '0 4px 12px rgba(99, 102, 241, 0.2)',
                  },
                },
                '& .MuiInputLabel-root': {
                  fontSize: '0.9rem',
                },
              }}
            >
              <Select
                id="input-method"
                value={inputMethod}
                onChange={handleInputMethodChange}
                displayEmpty
                sx={{
                  backgroundColor: 'rgba(2, 6, 23, 0.8)',
                  '& .MuiSelect-select': {
                    textAlign: 'center',
                    paddingRight: '32px', // Ensure text doesn't overlap with the dropdown icon
                  },
                  '&:hover': {
                    backgroundColor: 'rgba(15, 23, 42, 0.9)',
                  },
                  '&.Mui-focused': {
                    backgroundColor: 'rgba(15, 23, 42, 0.95)',
                  },
                }}
                MenuProps={{
                  PaperProps: {
                    sx: {
                      backgroundColor: 'rgba(2, 6, 23, 0.95)',
                      backdropFilter: 'blur(8px)',
                      '& .MuiMenuItem-root': {
                        justifyContent: 'center',
                        '&:hover': {
                          backgroundColor: 'rgba(15, 23, 42, 0.9)',
                        },
                        '&.Mui-selected': {
                          backgroundColor: 'rgba(99, 102, 241, 0.2)',
                        },
                      },
                    },
                  },
                }}
              >
                <MenuItem value="" sx={{ justifyContent: 'center' }}>Sélectionner une méthode</MenuItem>
                <MenuItem value="excel" sx={{ justifyContent: 'center' }}>Fichier Excel</MenuItem>
                <MenuItem value="manual" sx={{ justifyContent: 'center' }}>Saisie Manuelle</MenuItem>
              </Select>
            </FormControl>

            {/* Render the Continuer button inline when manual entry is selected */}
            {inputMethod === 'manual' && (
              <Button
                variant="contained"
                color="primary"
                startIcon={<EditIcon />}
                onClick={handleManualEntry}
                sx={{
                  py: 1.5,
                  px: 4,
                  borderRadius: 2,
                  boxShadow: '0 4px 6px rgba(99, 102, 241, 0.2)',
                  '&:hover': {
                    boxShadow: '0 6px 8px rgba(99, 102, 241, 0.4)',
                    transform: 'translateY(-2px)',
                  },
                  transition: 'all 0.2s ease',
                  width: { xs: '100%', sm: 'auto' }, // Full width on mobile, auto on larger screens
                }}
                className="btn-pulse"
              >
                Continuer
              </Button>
            )}
          </Box>
        </Box>

        {inputMethod === 'excel' && (
          <Box
            className="input-method-container"
            sx={{
              background: 'rgba(15, 23, 42, 0.6)',
              borderRadius: 3,
              p: 3,
              border: '1px solid rgba(99, 102, 241, 0.1)',
            }}
          >
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} sm={8}>
                <Box
                  sx={{
                    border: '2px dashed rgba(99, 102, 241, 0.3)',
                    borderRadius: 2,
                    p: 3,
                    textAlign: 'center',
                    transition: 'all 0.2s ease',
                    '&:hover': {
                      borderColor: 'primary.main',
                      backgroundColor: 'rgba(99, 102, 241, 0.05)',
                    },
                    position: 'relative',
                  }}
                >
                  <Input
                    type="file"
                    inputProps={{ accept: '.xlsx, .xls' }}
                    onChange={handleFileChange}
                    fullWidth
                    sx={{
                      opacity: 0,
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      cursor: 'pointer',
                      zIndex: 2,
                    }}
                  />
                  <UploadFileIcon
                    color="primary"
                    sx={{
                      fontSize: 40,
                      mb: 1,
                      opacity: 0.8,
                    }}
                  />
                  <Typography variant="body2" color="text.secondary">
                    {file ? file.name : 'Glissez votre fichier Excel ici ou cliquez pour parcourir'}
                  </Typography>
                  {file && (
                    <Typography variant="caption" color="primary" sx={{ display: 'block', mt: 1 }}>
                      Fichier sélectionné
                    </Typography>
                  )}
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<UploadFileIcon />}
                  onClick={handleFileUpload}
                  disabled={!file || loading}
                  fullWidth
                  sx={{
                    py: 1.5,
                    boxShadow: '0 4px 6px rgba(99, 102, 241, 0.2)',
                    '&:not(:disabled):hover': {
                      boxShadow: '0 6px 8px rgba(99, 102, 241, 0.4)',
                      transform: 'translateY(-2px)',
                    },
                    transition: 'all 0.2s ease',
                  }}
                  className={!file || loading ? '' : 'btn-pulse'}
                >
                  {loading ? 'Chargement...' : 'Traiter'}
                </Button>
              </Grid>
            </Grid>
          </Box>
        )}



        {error && (
          <Box
            sx={{
              mt: 3,
              p: 2,
              borderRadius: 2,
              backgroundColor: 'rgba(239, 68, 68, 0.1)',
              border: '1px solid rgba(239, 68, 68, 0.2)',
              display: 'flex',
              alignItems: 'flex-start',
              gap: 1.5,
            }}
          >
            <Box
              component="span"
              sx={{
                display: 'inline-flex',
                p: 0.5,
                borderRadius: '50%',
                backgroundColor: 'rgba(239, 68, 68, 0.2)',
                color: 'error.main',
                mt: 0.5,
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
            </Box>
            <Box sx={{ flex: 1 }}>
              <Typography variant="subtitle2" color="error.main" sx={{ fontWeight: 600, mb: 0.5 }}>
                Erreur
              </Typography>
              <Typography variant="body2" color="error.main" sx={{ opacity: 0.9 }}>
                {error}
              </Typography>
            </Box>
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default Home;
