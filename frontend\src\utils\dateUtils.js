/**
 * Utility functions for date formatting and manipulation
 */

/**
 * Format a date to DD/MM/YYYY HH:MM
 * @param {Date|string} date - Date to format
 * @returns {string} - Formatted date string
 */
export const formatDate = (date) => {
  if (!date) return '';
  
  const d = new Date(date);
  
  // Check if date is valid
  if (isNaN(d.getTime())) return '';
  
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  
  return `${day}/${month}/${year} ${hours}:${minutes}`;
};

/**
 * Format a date to DD/MM/YYYY
 * @param {Date|string} date - Date to format
 * @returns {string} - Formatted date string
 */
export const formatDateOnly = (date) => {
  if (!date) return '';
  
  const d = new Date(date);
  
  // Check if date is valid
  if (isNaN(d.getTime())) return '';
  
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();
  
  return `${day}/${month}/${year}`;
};

/**
 * Ensure a date is within working hours (8:00-12:00, 12:30-16:00)
 * @param {Date} date - Date to adjust
 * @returns {Date} - Adjusted date
 */
export const ensureWorkingHours = (date) => {
  if (!date) return new Date();
  
  const d = new Date(date);
  
  // Check if date is valid
  if (isNaN(d.getTime())) return new Date();
  
  const hours = d.getHours();
  const minutes = d.getMinutes();
  
  // If outside working hours, adjust
  if (hours < 8) {
    // Before work hours, set to 8:00 AM
    d.setHours(8, 0, 0, 0);
  } else if (hours === 12 && minutes < 30) {
    // During lunch break, set to 12:30 PM
    d.setHours(12, 30, 0, 0);
  } else if (hours >= 16) {
    // After work hours, set to 4:00 PM
    d.setHours(16, 0, 0, 0);
  }
  
  return d;
};

/**
 * Parse a date from various formats
 * @param {any} value - Value to parse as date
 * @returns {Date} - Parsed date or current date if parsing fails
 */
export const parseDate = (value) => {
  if (!value) return new Date();
  
  try {
    // If it's already a Date object
    if (value instanceof Date) {
      return new Date(value);
    }
    
    // If it's an ISO string
    if (typeof value === 'string' && value.includes('T')) {
      return new Date(value);
    }
    
    // If it's a DD/MM/YYYY format
    if (typeof value === 'string' && value.includes('/')) {
      const parts = value.split('/');
      if (parts.length === 3) {
        // Assuming DD/MM/YYYY format
        return new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
      }
    }
    
    // If it's an Excel serial number
    if (typeof value === 'number' && !isNaN(value)) {
      // Excel dates are number of days since 1900-01-01
      const excelEpoch = new Date(1900, 0, 1);
      return new Date(excelEpoch.getTime() + (value - 1) * 24 * 60 * 60 * 1000);
    }
    
    // Default fallback
    return new Date(value);
  } catch (e) {
    console.error('Error parsing date:', e);
    return new Date();
  }
};
