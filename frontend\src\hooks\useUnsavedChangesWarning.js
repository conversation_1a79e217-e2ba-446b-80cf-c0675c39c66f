import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { hasUnsavedChanges } from '../services/api';

/**
 * Custom hook to handle unsaved changes warning when navigating away from a page
 * @returns {Object} - Object containing state and handlers for unsaved changes dialog
 */
const useUnsavedChangesWarning = () => {
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);
  const navigate = useNavigate();

  // Handle navigation with unsaved changes
  const handleNavigation = useCallback((path) => {
    if (hasUnsavedChanges) {
      setPendingNavigation(path);
      setShowUnsavedDialog(true);
    } else {
      navigate(path);
    }
  }, [navigate]);

  // Handle dialog confirmation (OK button)
  const handleConfirmNavigation = useCallback(() => {
    setShowUnsavedDialog(false);
    // Reset the unsaved changes flag when the user confirms navigation
    hasUnsavedChanges = false;
    if (pendingNavigation) {
      navigate(pendingNavigation);
    }
  }, [navigate, pendingNavigation]);

  // Handle dialog cancellation
  const handleCancelNavigation = useCallback(() => {
    setShowUnsavedDialog(false);
    setPendingNavigation(null);
  }, []);

  // Add event listener for beforeunload
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
        return '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  return {
    showUnsavedDialog,
    handleNavigation,
    handleConfirmNavigation,
    handleCancelNavigation
  };
};

export default useUnsavedChangesWarning;
