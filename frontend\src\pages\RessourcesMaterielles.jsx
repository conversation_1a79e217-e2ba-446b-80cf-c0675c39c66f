import React, { useState, useEffect } from 'react';
import { Container, Typography, Alert, Snackbar, Box, Paper, Divider, Button, Dialog, DialogTitle, DialogContent, DialogActions, TextField, MenuItem } from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import AddIcon from '@mui/icons-material/Add';
import DataTable from '../components/DataTable';
import UnsavedChangesDialog from '../components/UnsavedChangesDialog';
import BackButton from '../components/BackButton';
import { ressourcesMaterielles, saveAllData, checkUnsavedChanges } from '../services/navbarApi';

const RessourcesMaterielles = () => {
  const [ressourcesMatData, setRessourcesMatData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [newRowData, setNewRowData] = useState({
    codeProduit: '',
    composant: '',
    designation: '',
    qteNette: '',
    perteCont: '',
    perteTech: '',
    qteBrut: '',
    seuil: '',
    unite: '',
    moq: '',
    delaiAppro: ''
  });

  const columns = [
    { field: 'codeProduit', headerName: 'Code Produit', type: 'string' },
    { field: 'composant', headerName: 'Composant', type: 'number' },
    { field: 'designation', headerName: 'Designation', type: 'string' },
    { field: 'qteNette', headerName: 'Qté Nette', type: 'number' },
    { field: 'perteCont', headerName: 'Perte Cont.', type: 'number' },
    { field: 'perteTech', headerName: 'Perte Tech.', type: 'number' },
    { field: 'qteBrut', headerName: 'Qté Brut', type: 'number' },
    { field: 'seuil', headerName: 'SEUIL', type: 'number' },
    { field: 'unite', headerName: 'Unité', type: 'string' },
    { field: 'moq', headerName: 'MOQ', type: 'number' },
    { field: 'delaiAppro', headerName: 'Délai d\'appro (jours calendaires)', type: 'number' },
  ];

  useEffect(() => {
    fetchRessourcesMaterielles(false); // Use cached data if available for faster loading
  }, []);

  // Add beforeunload event listener to show browser's native "unsaved changes" alert
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (checkUnsavedChanges()) {
        event.preventDefault();
        event.returnValue = ''; // This triggers the browser's native dialog
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const fetchRessourcesMaterielles = async (forceRefresh = false) => {
    try {
      setLoading(true);
      const data = await ressourcesMaterielles.getAll(forceRefresh); // Only force refresh when explicitly requested
      setRessourcesMatData(data);
      setError('');
    } catch (error) {
      setError('Erreur lors du chargement des ressources matérielles');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = async (newItem) => {
    try {
      // Add an ID if not present
      if (!newItem.id) {
        newItem.id = Date.now().toString();
      }

      // Convert numeric fields
      newItem.composant = parseInt(newItem.composant);
      newItem.qteNette = parseFloat(newItem.qteNette);
      newItem.perteCont = parseFloat(newItem.perteCont);
      newItem.perteTech = parseFloat(newItem.perteTech);
      newItem.qteBrut = parseFloat(newItem.qteBrut);
      newItem.seuil = parseFloat(newItem.seuil);
      newItem.moq = parseFloat(newItem.moq);
      newItem.delaiAppro = parseInt(newItem.delaiAppro);

      const addedItem = await ressourcesMaterielles.add(newItem);
      // Refresh data from the API to ensure we have the latest state
      const updatedData = await ressourcesMaterielles.getAll();
      setRessourcesMatData(updatedData);
      showNotification('Ressource matérielle ajoutée avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de l\'ajout de la ressource matérielle', 'error');
    }
  };

  const handleEdit = async (updatedItem) => {
    try {
      // Convert numeric fields
      updatedItem.composant = parseInt(updatedItem.composant);
      updatedItem.qteNette = parseFloat(updatedItem.qteNette);
      updatedItem.perteCont = parseFloat(updatedItem.perteCont);
      updatedItem.perteTech = parseFloat(updatedItem.perteTech);
      updatedItem.qteBrut = parseFloat(updatedItem.qteBrut);
      updatedItem.seuil = parseFloat(updatedItem.seuil);
      updatedItem.moq = parseFloat(updatedItem.moq);
      updatedItem.delaiAppro = parseInt(updatedItem.delaiAppro);

      const editedItem = await ressourcesMaterielles.update(updatedItem);
      setRessourcesMatData(ressourcesMatData.map(item => item.id === editedItem.id ? editedItem : item));
      showNotification('Ressource matérielle mise à jour avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la mise à jour de la ressource matérielle', 'error');
    }
  };

  const handleDelete = async (id) => {
    try {
      await ressourcesMaterielles.delete(id);
      setRessourcesMatData(ressourcesMatData.filter(item => item.id !== id));
      showNotification('Ressource matérielle supprimée avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la suppression de la ressource matérielle', 'error');
    }
  };

  const handleSave = async () => {
    try {
      // Save all data to backend
      const result = await saveAllData();

      // Always show success message since we've modified saveAllData to always return success: true
      // when the data is saved to localStorage, even if there were backend errors
      showNotification(result.message || 'Changements sauvegardés avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la sauvegarde des changements', 'error');
    }
  };

  // Handle navigation with unsaved changes
  const handleNavigation = (path) => {
    if (checkUnsavedChanges()) {
      setPendingNavigation(path);
      setShowUnsavedDialog(true);
    } else {
      window.location.href = path;
    }
  };

  // Handle dialog confirmation (OK button)
  const handleConfirmNavigation = () => {
    setShowUnsavedDialog(false);
    if (pendingNavigation) {
      window.location.href = pendingNavigation;
    }
  };

  // Handle dialog cancellation
  const handleCancelNavigation = () => {
    setShowUnsavedDialog(false);
    setPendingNavigation(null);
  };

  const showNotification = (message, severity) => {
    setNotification({ open: true, message, severity });
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  // Handle opening add dialog
  const handleOpenAddDialog = () => {
    setNewRowData({
      codeProduit: '',
      composant: '',
      designation: '',
      qteNette: '',
      perteCont: '',
      perteTech: '',
      qteBrut: '',
      seuil: '',
      unite: '',
      moq: '',
      delaiAppro: ''
    });
    setShowAddDialog(true);
  };

  // Handle closing add dialog
  const handleCloseAddDialog = () => {
    setShowAddDialog(false);
    setNewRowData({
      codeProduit: '',
      composant: '',
      designation: '',
      qteNette: '',
      perteCont: '',
      perteTech: '',
      qteBrut: '',
      seuil: '',
      unite: '',
      moq: '',
      delaiAppro: ''
    });
  };

  // Handle input changes in add dialog
  const handleNewRowChange = (field, value) => {
    setNewRowData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle confirming add from dialog
  const handleConfirmAdd = async () => {
    // Validate required fields
    if (!newRowData.codeProduit || !newRowData.composant || !newRowData.designation) {
      setNotification({
        open: true,
        message: 'Veuillez remplir tous les champs obligatoires (Code Produit, Composant, Désignation)',
        severity: 'error'
      });
      return;
    }

    try {
      await handleAdd(newRowData);
      handleCloseAddDialog();
      setNotification({
        open: true,
        message: 'Ligne ajoutée avec succès',
        severity: 'success'
      });
    } catch (error) {
      setNotification({
        open: true,
        message: 'Erreur lors de l\'ajout de la ligne',
        severity: 'error'
      });
    }
  };

  return (
    <Container sx={{ backgroundColor: 'transparent', maxWidth: '100%', padding: 0 }}>
      <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
        <BackButton />
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper elevation={3} sx={{ p: 2, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="h2">
            Ressources Matérielles
          </Typography>
        </Box>

        <Divider sx={{ mb: 2 }} />

        {/* Group data by Code Produit and display each group */}
        {Array.from(new Set(ressourcesMatData.map(item => item.codeProduit)))
          .sort() // Sort product codes alphabetically
          .map(codeProduit => {
            // Get data for this product
            const productData = ressourcesMatData.filter(item => item.codeProduit === codeProduit);

            return (
              <Box
                key={codeProduit}
                sx={{
                  mb: 4,
                  borderRadius: '8px',
                  overflow: 'hidden',
                  boxShadow: '0 3px 10px rgba(0, 0, 0, 0.1)'
                }}
              >
                {/* Product title */}
                <Box
                  sx={{
                    p: 2,
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
                    display: 'flex',
                    alignItems: 'center'
                  }}
                >
                  <Typography
                    variant="h6"
                    component="h3"
                    sx={{
                      fontWeight: 'bold',
                      color: 'primary.main'
                    }}
                  >
                    Produit: {codeProduit}
                  </Typography>
                </Box>

                {/* Table for this product */}
                <Box sx={{ p: 0 }}>
                  <DataTable
                    title=""
                    columns={columns}
                    data={productData}
                    onAdd={null} // Remove add button from individual tables
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    onSave={null} // Remove save button from individual tables
                    loading={loading}
                  />
                </Box>
              </Box>
            );
          })
        }

        {/* Show message if no data */}
        {ressourcesMatData.length === 0 && !loading && (
          <Typography variant="body1" align="center" sx={{ my: 4 }}>
            Aucune donnée de ressources matérielles disponible.
          </Typography>
        )}

        {/* Action buttons at the end of all tables */}
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 2, position: 'relative' }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleOpenAddDialog}
            sx={{
              py: 1.2,
              px: 3,
              borderRadius: 2,
              boxShadow: '0 4px 6px rgba(58, 134, 255, 0.2)',
              '&:hover': {
                boxShadow: '0 6px 8px rgba(58, 134, 255, 0.4)',
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.2s ease',
            }}
          >
            Ajouter une ligne
          </Button>

          <Button
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            sx={{
              py: 1.2,
              px: 3,
              borderRadius: 2,
              boxShadow: '0 4px 6px rgba(58, 134, 255, 0.2)',
              '&:hover': {
                boxShadow: '0 6px 8px rgba(58, 134, 255, 0.4)',
                transform: 'translateY(-2px)',
              },
              transition: 'all 0.2s ease',
              position: 'absolute',
              right: 0,
            }}
          >
            Sauvegarder les modifications
          </Button>
        </Box>
      </Paper>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity}>
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Unsaved Changes Dialog */}
      <UnsavedChangesDialog
        open={showUnsavedDialog}
        onCancel={handleCancelNavigation}
        onConfirm={handleConfirmNavigation}
      />

      {/* Add Row Dialog */}
      <Dialog open={showAddDialog} onClose={handleCloseAddDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Ajouter une nouvelle ligne</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              label="Code Produit"
              value={newRowData.codeProduit}
              onChange={(e) => handleNewRowChange('codeProduit', e.target.value)}
              required
              fullWidth
            />
            <TextField
              label="Composant"
              type="number"
              value={newRowData.composant}
              onChange={(e) => handleNewRowChange('composant', e.target.value)}
              required
              fullWidth
            />
            <TextField
              label="Désignation"
              value={newRowData.designation}
              onChange={(e) => handleNewRowChange('designation', e.target.value)}
              required
              fullWidth
            />
            <TextField
              label="Qté Nette"
              type="number"
              value={newRowData.qteNette}
              onChange={(e) => handleNewRowChange('qteNette', e.target.value)}
              fullWidth
            />
            <TextField
              label="Perte Cont."
              type="number"
              value={newRowData.perteCont}
              onChange={(e) => handleNewRowChange('perteCont', e.target.value)}
              fullWidth
            />
            <TextField
              label="Perte Tech."
              type="number"
              value={newRowData.perteTech}
              onChange={(e) => handleNewRowChange('perteTech', e.target.value)}
              fullWidth
            />
            <TextField
              label="Qté Brut"
              type="number"
              value={newRowData.qteBrut}
              onChange={(e) => handleNewRowChange('qteBrut', e.target.value)}
              fullWidth
            />
            <TextField
              label="SEUIL"
              type="number"
              value={newRowData.seuil}
              onChange={(e) => handleNewRowChange('seuil', e.target.value)}
              fullWidth
            />
            <TextField
              label="Unité"
              value={newRowData.unite}
              onChange={(e) => handleNewRowChange('unite', e.target.value)}
              fullWidth
            />
            <TextField
              label="MOQ"
              type="number"
              value={newRowData.moq}
              onChange={(e) => handleNewRowChange('moq', e.target.value)}
              fullWidth
            />
            <TextField
              label="Délai d'appro (jours calendaires)"
              type="number"
              value={newRowData.delaiAppro}
              onChange={(e) => handleNewRowChange('delaiAppro', e.target.value)}
              fullWidth
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseAddDialog} color="secondary">
            Annuler
          </Button>
          <Button onClick={handleConfirmAdd} variant="contained" color="primary">
            Ajouter
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default RessourcesMaterielles;
