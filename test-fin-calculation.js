// Test file to debug Fin calculation
// Let's manually trace through the K170 example

console.log('=== TESTING FIN CALCULATION FOR K170 ===');

// Test data - using more realistic values
const product = {
  codeProduit: 'K170',
  quantiteDemandee: 2000,  // More realistic quantity
  quantiteeOuverte: 1000,  // Half completed
  startDate: '2025-04-22T08:00:00'
};

const fluxProduits = [
  { codeProduit: 'K170', ordre: 1, poste: 'Surmoulage', machinesPossibles: 'Machine BA 600', tempsCycle: 0.6 },
  { codeProduit: 'K170', ordre: 2, poste: 'Graissage', machinesPossibles: 'Machine graissage 3', tempsCycle: 0.15 },
  { codeProduit: 'K170', ordre: 3, poste: 'Soudage', machinesPossibles: 'Machine soudage 2', tempsCycle: 0.2 },
];

const machines = [
  { nomMachine: 'Machine BA 600', tempsSetup: 60 },
  { nomMachine: 'Machine graissage 3', tempsSetup: 7 },
  { nomMachine: 'Machine soudage 2', tempsSetup: 10 },
];

console.log('\n--- INPUT DATA ---');
console.log('Product:', product);
console.log('Quantité Demandée:', product.quantiteDemandee);
console.log('Quantité Ouverte:', product.quantiteeOuverte);

let previousMachineResult = 0;
const planning = [];

fluxProduits.forEach((step, i) => {
  console.log(`\n--- MACHINE ${i + 1}: ${step.machinesPossibles} ---`);

  const machine = machines.find(m => m.nomMachine === step.machinesPossibles);
  const qty = product.quantiteDemandee;
  const qtyOuverte = product.quantiteeOuverte;
  const cycle = step.tempsCycle;
  const setup = machine.tempsSetup;

  // Calculate Temps Total
  const totalTime = qty * cycle + setup;
  console.log(`Temps Total = ${qty} × ${cycle} + ${setup} = ${totalTime} min`);

  let result, finTime;

  if (qtyOuverte === 0) {
    // Case 1
    console.log('Case 1: Quantité Ouverte = 0');
    result = totalTime;
    finTime = totalTime;
    console.log(`Fin = Début + ${totalTime} min`);
  } else {
    // Case 2: Apply recursive formula Result[i] = Temps Total[i] + Result[i-1] - (Temps Cycle[i-1] × Quantité Ouverte)
    if (i === 0) {
      // First machine: Result[0] = Temps Total[0]
      console.log('Case 2 - First machine: Result = Temps Total');
      result = totalTime;
      console.log(`Result[0] = ${totalTime} min`);
    } else {
      // Subsequent machines: Result[i] = Temps Total[i] + Result[i-1] - (Temps Cycle[i-1] × Quantité Ouverte)
      console.log('Case 2 - Subsequent machine: Recursive formula');
      const prevCycle = fluxProduits[i-1].tempsCycle;
      result = totalTime + previousMachineResult - (qtyOuverte * prevCycle);
      console.log(`Result[${i}] = ${totalTime} + ${previousMachineResult} - (${qtyOuverte} × ${prevCycle})`);
      console.log(`Result[${i}] = ${totalTime} + ${previousMachineResult} - ${qtyOuverte * prevCycle} = ${result} min`);
    }

    finTime = result;
    previousMachineResult = result;
    console.log(`Fin = Début + ${result} min`);
  }

  // Store in planning
  planning.push({
    machine: step.machinesPossibles,
    tempsTotal: totalTime,
    result: result,
    finDuration: finTime
  });

  console.log(`Stored: Temps Total = ${totalTime}, Result = ${result}, Fin Duration = ${finTime}`);
});

console.log('\n--- FINAL PLANNING ---');
planning.forEach((entry, i) => {
  console.log(`${i + 1}. ${entry.machine}:`);
  console.log(`   Temps Total: ${entry.tempsTotal} min`);
  console.log(`   Result: ${entry.result} min`);
  console.log(`   Fin Duration: ${entry.finDuration} min`);
});
