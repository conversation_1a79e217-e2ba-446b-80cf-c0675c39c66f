@echo off
setlocal
echo Planification Michaud - Installation et demarrage
echo ================================================
echo.

REM Check if Node.js is installed
echo Verification de l'installation de Node.js...
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
  echo ERREUR: Node.js n'est pas installe ou n'est pas dans le PATH.
  echo Veuillez installer Node.js depuis https://nodejs.org/
  pause
  exit /b 1
)
node --version
echo.

REM Check if npm is installed
echo Verification de l'installation de npm...
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
  echo ERREUR: npm n'est pas installe ou n'est pas dans le PATH.
  echo Veuillez reinstaller Node.js depuis https://nodejs.org/
  pause
  exit /b 1
)
npm --version
echo.

REM Check backend and frontend directories
if not exist "backend" (
  echo ERREUR: Dossier 'backend' introuvable.
  pause
  exit /b 1
)

if not exist "frontend" (
  echo ERREUR: Dossier 'frontend' introuvable.
  pause
  exit /b 1
)

REM Create data directory and empty JSON files
echo Creation du repertoire de donnees...
if not exist "backend\data" mkdir "backend\data"

echo Initialisation des fichiers de donnees...
(for %%F in (
  fluxProduits.json
  machines.json
  transit.json
  ressourcesHumaines.json
  polyvalence.json
  ressourcesMaterielles.json
  stockMP.json
  produits.json
  productionPlanning.json
  planningMaterielles.json
  chargesMachines.json
  chargesRH.json
) do (
  if not exist "backend\data\%%F" echo [] > "backend\data\%%F"
))

REM Create assets directory for frontend if it doesn't exist
echo Creation du repertoire assets...
if not exist "frontend\src\assets" mkdir "frontend\src\assets"
if not exist "frontend\src\assets\images" mkdir "frontend\src\assets\images"

REM Create services directory for frontend if it doesn't exist
echo Creation du repertoire services...
if not exist "frontend\src\services" mkdir "frontend\src\services"

REM Create utils directory for frontend if it doesn't exist
echo Creation du repertoire utils...
if not exist "frontend\src\utils" mkdir "frontend\src\utils"

REM Create components directory for frontend if it doesn't exist
echo Creation du repertoire components...
if not exist "frontend\src\components" mkdir "frontend\src\components"

REM Create pages directory for frontend if it doesn't exist
echo Creation du repertoire pages...
if not exist "frontend\src\pages" mkdir "frontend\src\pages"

echo.

REM Install root dependencies first
echo Installation des dependances racine...
if not exist "package.json" (
  echo ERREUR: package.json manquant dans le dossier racine.
  pause
  exit /b 1
)

REM Check and install npm-run-all if missing
findstr /i /c:"\"npm-run-all\"" package.json >nul
if %ERRORLEVEL% NEQ 0 (
  echo Installation de 'npm-run-all'...
  npm install npm-run-all --save-dev
)

REM Check and install rimraf if missing
findstr /i /c:"\"rimraf\"" package.json >nul
if %ERRORLEVEL% NEQ 0 (
  echo Installation de 'rimraf'...
  npm install rimraf --save-dev
)

REM Install root dependencies
npm install
echo.

REM Ensure all backend dependencies are installed
echo Verification et installation des dependances backend...
pushd backend
if not exist "package.json" (
  echo ERREUR: package.json manquant dans le dossier backend.
  popd
  pause
  exit /b 1
)

REM Check and install critical backend dependencies
echo Verification des dependances critiques du backend...
for %%D in (express cors body-parser multer xlsx mongoose nodemon) do (
  findstr /i /c:"\"%%D\"" package.json >nul
  if %ERRORLEVEL% NEQ 0 (
    echo Installation de '%%D' dans le backend...
    if "%%D"=="nodemon" (
      npm install %%D --save-dev
    ) else (
      npm install %%D --save
    )
  )
)
popd

REM Ensure all frontend dependencies are installed
echo Verification et installation des dependances frontend...
pushd frontend
if not exist "package.json" (
  echo ERREUR: package.json manquant dans le dossier frontend.
  popd
  pause
  exit /b 1
)

REM Check and install critical frontend dependencies
echo Verification des dependances critiques du frontend...
for %%D in (react react-dom react-router-dom axios xlsx vite) do (
  findstr /i /c:"\"%%D\"" package.json >nul
  if %ERRORLEVEL% NEQ 0 (
    echo Installation de '%%D' dans le frontend...
    if "%%D"=="vite" (
      npm install %%D --save-dev
    ) else (
      npm install %%D --save
    )
  )
)

REM Check and install Material-UI dependencies
echo Verification des dependances Material-UI...
for %%D in (@mui/material @mui/icons-material @mui/x-data-grid @emotion/react @emotion/styled) do (
  findstr /i /c:"\"%%D\"" package.json >nul
  if %ERRORLEVEL% NEQ 0 (
    echo Installation de '%%D' dans le frontend...
    npm install %%D --save
  )
)

REM Check and install dev dependencies
echo Verification des dependances de developpement...
for %%D in (@vitejs/plugin-react eslint) do (
  findstr /i /c:"\"%%D\"" package.json >nul
  if %ERRORLEVEL% NEQ 0 (
    echo Installation de '%%D' dans le frontend...
    npm install %%D --save-dev
  )
)
popd
echo.

REM Installer les dependances avec le script npm
echo Installation finale des dependances du projet...
npm run install:all
if %ERRORLEVEL% NEQ 0 (
  echo ERREUR: Echec de l'installation des dependances.
  pause
  exit /b 1
)

REM Verification finale des installations
echo.
echo Verification finale des installations...
echo =====================================

REM Check backend dependencies
echo Verification backend:
pushd backend
if exist "node_modules\express" (
  echo   [OK] Express installe
) else (
  echo   [ERREUR] Express manquant
)
if exist "node_modules\cors" (
  echo   [OK] CORS installe
) else (
  echo   [ERREUR] CORS manquant
)
if exist "node_modules\multer" (
  echo   [OK] Multer installe
) else (
  echo   [ERREUR] Multer manquant
)
if exist "node_modules\xlsx" (
  echo   [OK] XLSX installe
) else (
  echo   [ERREUR] XLSX manquant
)
popd

REM Check frontend dependencies
echo Verification frontend:
pushd frontend
if exist "node_modules\react" (
  echo   [OK] React installe
) else (
  echo   [ERREUR] React manquant
)
if exist "node_modules\@mui\material" (
  echo   [OK] Material-UI installe
) else (
  echo   [ERREUR] Material-UI manquant
)
if exist "node_modules\@mui\x-data-grid" (
  echo   [OK] MUI DataGrid installe
) else (
  echo   [ERREUR] MUI DataGrid manquant
)
if exist "node_modules\react-router-dom" (
  echo   [OK] React Router installe
) else (
  echo   [ERREUR] React Router manquant
)
if exist "node_modules\axios" (
  echo   [OK] Axios installe
) else (
  echo   [ERREUR] Axios manquant
)
if exist "node_modules\vite" (
  echo   [OK] Vite installe
) else (
  echo   [ERREUR] Vite manquant
)
popd

REM Check root dependencies
echo Verification racine:
if exist "node_modules\npm-run-all" (
  echo   [OK] npm-run-all installe
) else (
  echo   [ERREUR] npm-run-all manquant
)

echo.
echo Installation terminee avec succes !
echo.

REM Script pour effacer le stockage local
echo Creation du script pour effacer le cache local...
(
echo @echo off
echo echo Planification Michaud - Nettoyage du stockage local
echo echo ===================================================
echo echo.
echo echo Pour effacer le cache local du navigateur :
echo echo 1. Ouvrez http://localhost:5173
echo echo 2. Appuyez sur F12 > onglet Console
echo echo 3. Copiez-collez :
echo echo.
echo echo    localStorage.removeItem^('planificationMichaudData'^);
echo echo.
echo echo 4. Appuyez sur Entrée, puis rafraichissez la page.
pause
) > clear-localStorage.bat
echo.

REM Demarrage de l'application
echo Demarrage de l'application...
npm run start

echo.
echo Application demarree avec succes !
echo Backend: http://localhost:5000
echo Frontend: http://localhost:5173
pause
endlocal
