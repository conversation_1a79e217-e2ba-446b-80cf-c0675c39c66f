/**
 * Custom planning generation function that creates a production schedule based on products and resources
 * @param {Object} data - Object containing all the necessary data for planning generation
 * @param {Array} data.produits - List of products to schedule
 * @param {Array} data.fluxProduits - List of product flow steps
 * @param {Array} data.machines - List of available machines
 * @param {Array} data.polyvalence - List of operator skills
 * @param {Array} data.ressourcesHumaines - List of human resources
 * @param {Array} data.transit - List of transit times between machines
 * @returns {Array} - Generated planning schedule
 */
/**
 * Validate all product deadlines before starting planning generation
 * @param {Array} produits - List of products to validate
 * @param {Array} fluxProduits - List of product flow steps
 * @param {Array} machines - List of available machines
 * @returns {Object} - Validation result with success flag and error message
 */
const validateDeadlines = (produits, fluxProduits, machines, workMinutesPerDay = 450, isRamadanHours = false) => {
  const errors = [];

  for (const product of produits) {
    // Get basic product info
    const codeProduit = product.codeProduit;
    const quantiteDemandee = parseInt(product.quantiteDemandee || product.QuantiteDemandee || product.quantite || product.Quantite || 1);

    // Parse deadline
    const deadlineValue = product.deadline || product.Deadline || product.dateEcheance || product.DateEcheance || product.dateLimite || product.DateLimite;

    if (!deadlineValue) {
      continue; // Skip products without deadlines
    }

    let deadline;
    try {
      if (deadlineValue instanceof Date) {
        deadline = new Date(deadlineValue);
      } else if (typeof deadlineValue === 'string' && deadlineValue.includes('T')) {
        deadline = new Date(deadlineValue);
      } else if (typeof deadlineValue === 'string' && deadlineValue.includes('/')) {
        const parts = deadlineValue.split('/');
        if (parts.length === 3) {
          deadline = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
          deadline.setHours(23, 59, 59, 999);
        }
      } else {
        deadline = new Date(deadlineValue);
      }
    } catch (e) {
      continue; // Skip products with invalid deadlines
    }

    // Parse start date
    const startDateValue = product.startDate || product.StartDate || product.dateDebut || product.DateDebut || product.date || product.Date;
    let startDate;
    try {
      if (startDateValue instanceof Date) {
        startDate = new Date(startDateValue);
      } else if (typeof startDateValue === 'string' && startDateValue.includes('T')) {
        startDate = new Date(startDateValue);
      } else if (typeof startDateValue === 'string' && startDateValue.includes('/')) {
        const parts = startDateValue.split('/');
        if (parts.length === 3) {
          startDate = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
        } else {
          startDate = new Date();
        }
      } else {
        startDate = new Date(startDateValue);
      }
    } catch (e) {
      startDate = new Date();
    }
    // Set start time based on working hours mode
    if (isRamadanHours) {
      startDate.setHours(8, 30, 0, 0); // 8:30 AM for Ramadan
    } else {
      startDate.setHours(8, 0, 0, 0);  // 8:00 AM for normal hours
    }

    // Calculate minimum time needed for this product
    const steps = fluxProduits
      .filter(fp => fp.codeProduit === codeProduit)
      .sort((a, b) => parseInt(a.ordre) - parseInt(b.ordre));

    let totalMinutesNeeded = 0;
    for (const step of steps) {
      const cycle = parseFloat(step.tempsCycle || step.TempsCycle || 0);
      const machine = machines.find(m => {
        const machineName = m.nomMachine || m.NomMachine || m.nom || m.Nom || m.name || m.Name;
        const possibleMachines = (step.machinesPossibles || '').split(',').map(m => m.trim());
        return possibleMachines.includes(machineName);
      });
      const setup = parseFloat(machine?.tempsSetup || machine?.TempsSetup || 0);

      totalMinutesNeeded += (quantiteDemandee * cycle) + setup;
    }

    // Convert to working days using the correct working minutes per day
    const workingDaysNeeded = Math.ceil(totalMinutesNeeded / workMinutesPerDay);

    // Calculate minimum completion date
    let minCompletionDate = new Date(startDate);
    let daysAdded = 0;
    while (daysAdded < workingDaysNeeded) {
      minCompletionDate.setDate(minCompletionDate.getDate() + 1);
      // Skip weekends
      if (minCompletionDate.getDay() !== 0 && minCompletionDate.getDay() !== 6) {
        daysAdded++;
      }
    }

    // Check if deadline is sufficient
    if (minCompletionDate > deadline) {
      const delayDays = Math.ceil((minCompletionDate - deadline) / (1000 * 60 * 60 * 24));
      errors.push({
        codeProduit,
        deadline: deadline.toLocaleDateString('fr-FR'),
        minCompletionDate: minCompletionDate.toLocaleDateString('fr-FR'),
        delayDays,
        quantiteDemandee,
        workingDaysNeeded
      });
    }
  }

  if (errors.length > 0) {
    let errorMessage = "⚠️ Veuillez prolonger le délai ou réduire la charge de travail";

    return {
      success: false,
      message: errorMessage,
      errors
    };
  }

  return { success: true };
};

const generateCustomPlanning = ({ produits, fluxProduits, machines, polyvalence, ressourcesHumaines, transit, isRamadanHours = false }) => {

  // Define work schedule constants (in minutes) based on working hours mode
  let WORK_START, WORK_END, BREAK_START, BREAK_END, WORK_MINUTES_PER_DAY;

  if (isRamadanHours) {
    // Ramadan working hours: 8:30 AM to 1:30 PM (no lunch break)
    WORK_START = 8 * 60 + 30;      // 8:30 AM (510 minutes)
    WORK_END = 13 * 60 + 30;       // 1:30 PM (810 minutes)
    BREAK_START = null;            // No lunch break during Ramadan
    BREAK_END = null;              // No lunch break during Ramadan
    WORK_MINUTES_PER_DAY = 300;    // 5 hours (300 minutes) per day
  } else {
    // Normal working hours: 8:00 AM to 4:00 PM with lunch break
    WORK_START = 8 * 60;           // 8:00 AM (480 minutes)
    WORK_END = 16 * 60;            // 4:00 PM (960 minutes)
    BREAK_START = 12 * 60;         // 12:00 PM (720 minutes)
    BREAK_END = 12 * 60 + 30;      // 12:30 PM (750 minutes)
    WORK_MINUTES_PER_DAY = 450;    // 7.5 hours (450 minutes) per day
  }

  // FIRST: Validate all deadlines before starting generation using the correct working hours
  const validation = validateDeadlines(produits, fluxProduits, machines, WORK_MINUTES_PER_DAY, isRamadanHours);

  if (!validation.success) {
    throw new Error(validation.message);
  }

  const planning = [];

  /**
   * Format a date to DD/MM/YYYY HH:MM
   * @param {Date} date - Date to format
   * @returns {string} - Formatted date string
   */
  const formatDateTime = (date) => {
    const d = new Date(date);
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const year = d.getFullYear();
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    return `${day}/${month}/${year} ${hours}:${minutes}`;
  };

  /**
   * Add minutes to a date considering work schedule and breaks
   * @param {Date} startDate - Starting date and time
   * @param {number} duration - Duration in minutes (Temps Total from Planning de Production)
   * @param {number} setupTime - Setup time in minutes from the Machines table
   * @returns {Date} - End date and time
   */
  const addMinutesConsideringSchedule = (startDate, duration, setupTime = 0) => {
    // For the Fin calculation, we use the Temps Total which now includes setup time
    // The Temps Total is calculated as Quantité Demandée × Temps Cycle + Temps Setup
    // So we don't need to add setup time separately anymore
    let totalDuration = duration;
    if (setupTime > 0) {
      totalDuration += setupTime;
    }



    let remaining = totalDuration;
    let current = new Date(startDate);
    let workedToday = 0;
    let currentDay = current.toDateString();

    // Ensure the start time is within working hours
    const currentHour = current.getHours();
    const currentMinute = current.getMinutes();
    const currentTimeInMinutes = currentHour * 60 + currentMinute;

    // If before work hours, move to work start time
    if (currentTimeInMinutes < WORK_START) {
      if (isRamadanHours) {
        current.setHours(8, 30, 0, 0); // 8:30 AM for Ramadan
      } else {
        current.setHours(8, 0, 0, 0);  // 8:00 AM for normal hours
      }
    }
    // If during lunch break (only for normal hours), move to 12:30 PM
    else if (!isRamadanHours && BREAK_START && BREAK_END && currentTimeInMinutes >= BREAK_START && currentTimeInMinutes < BREAK_END) {
      current.setHours(12, 30, 0, 0);
    }
    // If after work hours, move to next day at work start time
    else if (currentTimeInMinutes >= WORK_END) {
      current.setDate(current.getDate() + 1);
      if (isRamadanHours) {
        current.setHours(8, 30, 0, 0); // 8:30 AM for Ramadan
      } else {
        current.setHours(8, 0, 0, 0);  // 8:00 AM for normal hours
      }

      // Check if the next day is a weekend
      const nextDayOfWeek = current.getDay();
      if (nextDayOfWeek === 0) { // Sunday
        current.setDate(current.getDate() + 1); // Skip to Monday
      } else if (nextDayOfWeek === 6) { // Saturday
        current.setDate(current.getDate() + 2); // Skip to Monday
      }

      currentDay = current.toDateString();
      workedToday = 0;
    }

    while (remaining > 0) {
      // Check if it's a weekend (Saturday = 6, Sunday = 0)
      const dayOfWeek = current.getDay();
      if (dayOfWeek === 0 || dayOfWeek === 6) {
        // Skip to Monday if it's a weekend
        current.setDate(current.getDate() + (dayOfWeek === 0 ? 1 : 2));
        if (isRamadanHours) {
          current.setHours(8, 30, 0, 0); // Start at 8:30 AM for Ramadan
        } else {
          current.setHours(8, 0, 0, 0);  // Start at 8:00 AM for normal hours
        }
        currentDay = current.toDateString();
        workedToday = 0;
        continue;
      }

      // Reset work counter if it's a new day
      if (current.toDateString() !== currentDay) {
        workedToday = 0;
        currentDay = current.toDateString();
      }

      // Calculate current time in minutes since midnight
      let now = current.getHours() * 60 + current.getMinutes();

      // Handle work hours based on mode
      if (now < WORK_START) {
        // Before work hours, move to work start time
        if (isRamadanHours) {
          current.setHours(8, 30, 0, 0);
        } else {
          current.setHours(8, 0, 0, 0);
        }
        now = WORK_START;
      } else if (!isRamadanHours && BREAK_START && BREAK_END && now >= BREAK_START && now < BREAK_END) {
        // During lunch break (only for normal hours), move to 12:30 PM
        current.setHours(12, 30, 0, 0);
        now = BREAK_END;
      } else if (now >= WORK_END) {
        // After work hours, move to next day at work start time
        current.setDate(current.getDate() + 1);
        if (isRamadanHours) {
          current.setHours(8, 30, 0, 0);
        } else {
          current.setHours(8, 0, 0, 0);
        }

        // Check if the next day is a weekend
        const nextDayOfWeek = current.getDay();
        if (nextDayOfWeek === 0) { // Sunday
          current.setDate(current.getDate() + 1); // Skip to Monday
        } else if (nextDayOfWeek === 6) { // Saturday
          current.setDate(current.getDate() + 2); // Skip to Monday
        }

        currentDay = current.toDateString();
        workedToday = 0;
        continue;
      }

      // Calculate how much time we can work until next break or end of day
      let timeUntilNextBreak;
      if (isRamadanHours) {
        // Ramadan hours: no lunch break, work until end of day
        timeUntilNextBreak = WORK_END - now;
      } else {
        // Normal hours: check for lunch break
        if (now < BREAK_START) {
          // Morning shift, work until 12:00 PM
          timeUntilNextBreak = BREAK_START - now;
        } else {
          // Afternoon shift, work until 16:00 PM
          timeUntilNextBreak = WORK_END - now;
        }
      }

      // Calculate available time for today
      let availableToday = WORK_MINUTES_PER_DAY - workedToday;

      // Work for the minimum of: remaining time, time until next break, or available time today
      let timeToWork = Math.min(remaining, timeUntilNextBreak, availableToday);

      // Add the work time to current time
      current.setMinutes(current.getMinutes() + timeToWork);
      remaining -= timeToWork;
      workedToday += timeToWork;

      // If we've worked the maximum for today, move to next day
      if (workedToday >= WORK_MINUTES_PER_DAY) {
        current.setDate(current.getDate() + 1);
        if (isRamadanHours) {
          current.setHours(8, 30, 0, 0); // Start at 8:30 AM for Ramadan
        } else {
          current.setHours(8, 0, 0, 0);  // Start at 8:00 AM for normal hours
        }

        // Check if the next day is a weekend
        const nextDayOfWeek = current.getDay();
        if (nextDayOfWeek === 0) { // Sunday
          current.setDate(current.getDate() + 1); // Skip to Monday
        } else if (nextDayOfWeek === 6) { // Saturday
          current.setDate(current.getDate() + 2); // Skip to Monday
        }

        currentDay = current.toDateString();
        workedToday = 0;
      }
    }

    return new Date(current);
  };

  /**
   * Get transit time between two machines from the Transit table
   * @param {string} sourceMachine - Source machine name
   * @param {string} destinationMachine - Destination machine name
   * @returns {number} - Transit time in minutes, defaults to 0 if not found
   */
  const getTransitTime = (sourceMachine, destinationMachine) => {
    if (!sourceMachine || !destinationMachine) {
      return 0;
    }

    // Find transit time in the transit table
    const transitEntry = transit.find(t => {
      const source = (t.machineSource || '').trim();
      const destination = (t.machineDestination || '').trim();
      return source === sourceMachine.trim() && destination === destinationMachine.trim();
    });

    if (transitEntry) {
      return parseInt(transitEntry.tempsTransit) || 0;
    }

    return 0;
  };

  /**
   * Find when a machine becomes available after a given start time
   * @param {string} machineName - Name of the machine
   * @param {Date} desiredStartTime - Desired start time
   * @param {Array} planning - Current planning entries
   * @returns {Date} - Next available time for the machine
   */
  const findNextAvailableTime = (machineName, desiredStartTime, planning) => {
    // Get all existing assignments for this machine
    const machineAssignments = planning
      .filter(entry => entry.machine === machineName)
      .map(entry => ({
        start: new Date(entry.debut),
        end: new Date(entry.fin)
      }))
      .sort((a, b) => a.start.getTime() - b.start.getTime());

    let availableTime = new Date(desiredStartTime);

    // Check each existing assignment to find the next available slot
    for (const assignment of machineAssignments) {
      // If our desired time overlaps with this assignment, move to after this assignment
      if (availableTime < assignment.end && assignment.start < availableTime) {
        availableTime = new Date(assignment.end);

        // Ensure the new time is within working hours
        const timeInMinutes = availableTime.getHours() * 60 + availableTime.getMinutes();

        // If after work hours, move to next day at work start time
        if (timeInMinutes >= WORK_END) {
          availableTime.setDate(availableTime.getDate() + 1);
          if (isRamadanHours) {
            availableTime.setHours(8, 30, 0, 0); // 8:30 AM for Ramadan
          } else {
            availableTime.setHours(8, 0, 0, 0);  // 8:00 AM for normal hours
          }

          // Skip weekends
          const dayOfWeek = availableTime.getDay();
          if (dayOfWeek === 0) { // Sunday
            availableTime.setDate(availableTime.getDate() + 1); // Move to Monday
          } else if (dayOfWeek === 6) { // Saturday
            availableTime.setDate(availableTime.getDate() + 2); // Move to Monday
          }
        }
        // If during lunch break (only for normal hours), move to 12:30 PM
        else if (!isRamadanHours && BREAK_START && BREAK_END && timeInMinutes >= BREAK_START && timeInMinutes < BREAK_END) {
          availableTime.setHours(12, 30, 0, 0);
        }
      }
    }

    return availableTime;
  };

  /**
   * Find the best machine for a given post and list of possible machines (legacy function for compatibility)
   * @param {string} poste - Work post
   * @param {string} possibles - Comma-separated list of possible machines
   * @returns {Object} - Best machine object
   */
  const getBestMachine = (poste, possibles) => {
    if (!possibles) {
      return null;
    }

    // Split the comma-separated list of possible machines
    const possible = possibles.split(',').map(m => m.trim());

    // First, try to find an exact match
    let bestMachine = null;
    let bestScore = -1;

    for (const m of machines) {
      // Check for different field name variations
      const machinePoste = m.poste || m.Poste || m.post || m.Post;
      const machineName = m.nomMachine || m.NomMachine || m.nom || m.Nom || m.name || m.Name;
      const machineAvailable = m.disponibilite || m.Disponibilite || m.disponible || m.Disponible || m.available || m.Available;
      const machineCapacity = m.capacite || m.Capacite || m.capacity || m.Capacity;

      // Check if this machine matches our criteria
      const postMatches =
        machinePoste === poste ||
        machinePoste?.toLowerCase() === poste?.toLowerCase();

      // For exact machine name matching, only use exact matches
      // This ensures we get "Machine graissage 3" instead of just "Machine graissage"
      const nameMatches = possible.some(p => p === machineName);

      // Convert availability to boolean
      const availableStr = String(machineAvailable || '').toLowerCase();
      const isAvailable =
        availableStr === '1' ||
        availableStr === 'true' ||
        availableStr === 'oui' ||
        availableStr === 'yes';

      // Calculate a score for this machine (higher is better)
      let score = 0;
      if (postMatches) score += 10;
      if (nameMatches) score += 20;
      if (isAvailable) score += 50;
      if (machineCapacity) score += parseInt(machineCapacity) / 100; // Slight preference for higher capacity

      // Only consider available machines that match post and name
      if (postMatches && nameMatches && isAvailable && score > bestScore) {
        bestMachine = m;
        bestScore = score;
      }
    }

    if (bestMachine) {
      // Add normalized field names to make it easier to use later
      const normalizedMachine = {
        ...bestMachine,
        nomMachine: bestMachine.nomMachine || bestMachine.NomMachine || bestMachine.nom || bestMachine.Nom || bestMachine.name || bestMachine.Name,
        poste: bestMachine.poste || bestMachine.Poste || bestMachine.post || bestMachine.Post,
        tempsSetup: bestMachine.tempsSetup || bestMachine.TempsSetup || bestMachine.setupTime || bestMachine.SetupTime || 0
      };

      return normalizedMachine;
    }

    return null;
  };

  /**
   * Get the best available operator for a machine, checking for same-day conflicts
   * @param {string} machineName - Name of the machine
   * @param {Date} startTime - Start time of the task
   * @param {Array} planning - Current planning entries
   * @returns {Object} - Best available operator with name and minutes per day
   */
  const getBestAvailableOperator = (machineName, startTime, planning) => {
    const allOperators = getAllOperatorsForMachine(machineName);

    if (allOperators.length === 0) {
      return { name: 'inconnu', minutesPerDay: 0 };
    }

    // Filter out operators who are already working on the same day
    const availableOperators = allOperators.filter(operator => {
      return !isOperatorBusy(operator.name, startTime, planning);
    });

    if (availableOperators.length === 0) {
      return { name: 'inconnu', minutesPerDay: 0 };
    }

    // Sort available operators by experience level (highest first)
    availableOperators.sort((a, b) => b.minutesPerDay - a.minutesPerDay);

    return availableOperators[0];
  };

  /**
   * Get all operators who can work on a specific machine
   * @param {string} machineName - Name of the machine
   * @returns {Array} - Array of operators with their experience levels
   */
  const getAllOperatorsForMachine = (machineName) => {
    const machineToColumn = {
      'Machine BA 600': 'machineBA600',
      'Machine graissage 3': 'machineGraissage3',
      'Machine soudage 2': 'machineSoudage2',
      'Machine marquage 2': 'machineMarquage2',
      'Machine emballage 2': 'machineEmballage2',
      'Machine BA 500': 'machineBA500',
      'Machine graissage': 'machineGraissage',
      'Machine soudage 1': 'machineSoudage1',
      'Machine marquage 1': 'machineMarquage1',
      'Machine emballage 1': 'machineEmballage1'
    };

    const columnName = machineToColumn[machineName];
    if (!columnName) {
      return [];
    }

    const operators = [];
    polyvalence.forEach(operator => {
      const operatorName = operator.operateur || operator.nom || operator.Nom || operator.name || operator.Name || 'Unknown';
      const experienceLevel = parseFloat(operator[columnName] || 0);

      if (experienceLevel > 0) {
        // Use correct working hours: 7.5 hours for normal, 5 hours for Ramadan
        const heuresParJour = isRamadanHours ? 5 : 7.5;
        const minutesPerDay = experienceLevel * heuresParJour * 60;
        operators.push({ name: operatorName, minutesPerDay, experienceLevel });
      }
    });

    return operators;
  };

  /**
   * Check if an operator is busy on the same day
   * @param {string} operatorName - Name of the operator
   * @param {Date} startTime - Start time to check
   * @param {Array} planning - Current planning entries
   * @returns {boolean} - True if operator is busy on the same day, false if available
   */
  const isOperatorBusy = (operatorName, startTime, planning) => {
    // EXACT SAME LOGIC AS NORMAL HOURS - no changes for Ramadan
    const taskDate = new Date(startTime);
    taskDate.setHours(0, 0, 0, 0);

    for (const entry of planning) {
      if (entry.operateur === operatorName) {
        const entryStart = new Date(entry.debut);
        const entryEnd = new Date(entry.fin);
        const entryDate = new Date(entryStart);
        entryDate.setHours(0, 0, 0, 0);

        // Check if the task overlaps with any existing assignment for this operator
        // An operator cannot work on multiple machines during the same time period
        if (taskDate.getTime() >= entryDate.getTime() && taskDate.getTime() <= new Date(entryEnd).setHours(0, 0, 0, 0)) {
          console.log(`🚫 OPERATOR CONFLICT: ${operatorName} already working from ${entryStart.toDateString()} to ${entryEnd.toDateString()}`);
          return true;
        }
      }
    }

    return false;
  };

  /**
   * Check if a machine is busy during a specific time period
   * @param {string} machineName - Name of the machine
   * @param {Date} startTime - Start time to check
   * @param {Date} endTime - End time to check
   * @param {Array} planning - Current planning entries
   * @returns {boolean} - True if machine is busy during the time period, false if available
   */
  const isMachineBusy = (machineName, startTime, endTime, planning) => {
    for (const entry of planning) {
      if (entry.machine === machineName) {
        const entryStart = new Date(entry.debut);
        const entryEnd = new Date(entry.fin);

        // Check if the time periods overlap
        // Two time periods overlap if: start1 < end2 AND start2 < end1
        if (startTime < entryEnd && entryStart < endTime) {
          console.log(`🚫 MACHINE CONFLICT: ${machineName} already working from ${entryStart.toLocaleString()} to ${entryEnd.toLocaleString()}`);
          return true;
        }
      }
    }

    return false;
  };

  // Helper function to get priority order (for sorting)
  const getPriorityOrder = (priority) => {
    if (!priority) return 3; // Default lowest priority if not specified
    const priorityLower = priority.toLowerCase();
    if (priorityLower.includes('haute')) return 1;
    if (priorityLower.includes('moyenne')) return 2;
    if (priorityLower.includes('faible')) return 3;
    return 3; // Default to lowest priority
  };

  // Helper function to parse start date for sorting
  const parseStartDate = (product) => {
    const startDateValue = product.startDate || product.StartDate || product.dateDebut || product.DateDebut || product.date || product.Date;

    if (!startDateValue) return new Date();

    try {
      if (startDateValue instanceof Date) {
        return new Date(startDateValue);
      } else if (typeof startDateValue === 'string' && startDateValue.includes('T')) {
        return new Date(startDateValue);
      } else if (typeof startDateValue === 'string' && startDateValue.includes('/')) {
        const parts = startDateValue.split('/');
        if (parts.length === 3) {
          return new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
        }
      }
      return new Date(startDateValue);
    } catch (e) {
      return new Date();
    }
  };

  // Sort products by priority first, then by start date
  // This ensures that when machines have conflicts, higher priority products get scheduled first
  // and products with the same priority are scheduled by earliest start date
  const sortedProduits = [...produits].sort((a, b) => {
    const priorityDiff = getPriorityOrder(a.priorite) - getPriorityOrder(b.priorite);
    if (priorityDiff !== 0) {
      return priorityDiff; // Sort by priority first
    }

    // If same priority, sort by start date (earliest first)
    const dateA = parseStartDate(a);
    const dateB = parseStartDate(b);
    return dateA.getTime() - dateB.getTime();
  });

  console.log(`🔄 PRODUCT SCHEDULING ORDER: ${sortedProduits.map(p => `${p.codeProduit} (${p.priorite || 'N/A'}, ${parseStartDate(p).toLocaleDateString()})`).join(', ')}`);

  // Process each product (starting with highest priority)
  for (const p of sortedProduits) {
    // Track the previous machine's Result for case 2 Fin calculation
    let previousMachineResult = 0;

    // Find all steps for this product
    const steps = fluxProduits
      .filter(fp => fp.codeProduit === p.codeProduit)
      .sort((a, b) => parseInt(a.ordre) - parseInt(b.ordre));

    // Use startDate from the Produits table as the initial start date
    // Check all possible field names for start date
    const startDateValue = p.startDate || p.StartDate || p.dateDebut || p.DateDebut || p.date || p.Date;

    // Initialize the start date
    let start;
    if (startDateValue) {
      // Try to parse the date, handling different formats
      try {
        // If it's already a Date object
        if (startDateValue instanceof Date) {
          start = new Date(startDateValue);
        }
        // If it's an ISO string
        else if (typeof startDateValue === 'string' && startDateValue.includes('T')) {
          start = new Date(startDateValue);
        }
        // If it's a DD/MM/YYYY format
        else if (typeof startDateValue === 'string' && startDateValue.includes('/')) {
          const parts = startDateValue.split('/');
          if (parts.length === 3) {
            // Assuming DD/MM/YYYY format
            start = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
          } else {
            start = new Date();
          }
        }
        // Default fallback
        else {
          start = new Date(startDateValue);
        }
      } catch (e) {
        start = new Date();
      }
    } else {
      // If no start date provided, use current date
      start = new Date();
    }

    // IMPORTANT: Always set the initial time to work start time
    // This ensures we always start at a valid work hour
    if (isRamadanHours) {
      start.setHours(8, 30, 0, 0); // 8:30 AM for Ramadan hours
    } else {
      start.setHours(8, 0, 0, 0);  // 8:00 AM for normal hours
    }

    // Handle weekends - move to next working day (Monday)
    const dayOfWeek = start.getDay();

    if (dayOfWeek === 0) { // Sunday
      start.setDate(start.getDate() + 1); // Move to Monday
    } else if (dayOfWeek === 6) { // Saturday
      start.setDate(start.getDate() + 2); // Move to Monday
    }

    // Store the product's start date for use in first step
    const productStartDate = new Date(start);

    // Process each step
    for (let i = 0; i < steps.length; i++) {
      const step = steps[i];

      // Get basic step information first to calculate estimated times
      // Get cycle time from step - this comes from the Flux Produits table
      const cycleRaw = step.tempsCycle || step.TempsCycle || step['TempsCycle (min)'] || step.cycleTime || step.CycleTime || '0';
      const cycle = parseFloat(cycleRaw);

      // Get quantity from product - this comes from the Produits table
      const qtyRaw = p.quantiteDemandee || p.QuantiteDemandee || p.quantite || p.Quantite || p.quantity || p.Quantity || '1';
      const qty = parseInt(qtyRaw);

      // Get a preliminary machine to estimate setup time for conflict checking
      let preliminaryMachine;
      if (p.codeProduit === 'K170' && step.poste && step.poste.toLowerCase().includes('graissage')) {
        // Look for Machine graissage 3 specifically
        preliminaryMachine = machines.find(m => {
          const machineName = m.nomMachine || m.NomMachine || m.nom || m.Nom || m.name || m.Name;
          return machineName === 'Machine graissage 3';
        });
        if (!preliminaryMachine) {
          preliminaryMachine = getBestMachine(step.poste, step.machinesPossibles);
        }
      } else {
        preliminaryMachine = getBestMachine(step.poste, step.machinesPossibles);
      }

      // Get estimated setup time for duration calculation
      const estimatedSetupRaw = preliminaryMachine?.tempsSetup || preliminaryMachine?.TempsSetup || preliminaryMachine?.setupTime || preliminaryMachine?.SetupTime || '0';
      const estimatedSetup = parseFloat(estimatedSetupRaw);

      // Get Quantité Ouverte from product - this is used for Début calculation
      const qtyOuverteRaw = p.quantiteeOuverte !== undefined ? p.quantiteeOuverte :
                           (p.QuantiteeOuverte !== undefined ? p.QuantiteeOuverte :
                           (p.quantiteOuverte !== undefined ? p.quantiteOuverte :
                           (p.QuantiteOuverte !== undefined ? p.QuantiteOuverte : 0)));
      const qtyOuverte = parseInt(qtyOuverteRaw);

      // Calculate cycle time total (Quantité Demandée × Temps Cycle)
      const cycleTotal = cycle * qty;

      // Handle start time calculation with CORRECTED logic based on Quantité Ouverte
      // Apply the logic to all steps except the very first step of each product

      // Check if this is the first step of a new product
      const isFirstStepOfNewProduct = i === 0;

      if (planning.length > 0 && !isFirstStepOfNewProduct) {
        const prev = planning[planning.length - 1];

        // Get previous machine name for transit time calculation
        const previousMachine = prev.machine;

        // Get transit time between machines (using preliminary machine name for now)
        const preliminaryMachineName = preliminaryMachine?.nomMachine || preliminaryMachine?.NomMachine || preliminaryMachine?.nom || preliminaryMachine?.Nom || preliminaryMachine?.name || preliminaryMachine?.Name || 'N/A';
        const transitTime = getTransitTime(previousMachine, preliminaryMachineName);

        if (qtyOuverte === 0) {
          // CASE 1: If Quantité Ouverte = 0
          // Début = previous Fin + Temps Transit
          const previousFin = new Date(prev.fin);
          start = addMinutesConsideringSchedule(previousFin, transitTime);
        } else {
          // CASE 2: If Quantité Ouverte > 0
          // Début = previous Début + (Quantité Ouverte × Temps Cycle for previous machine) + Temps Setup + Temps Transit
          const previousDebut = new Date(prev.debut);

          // Find the Temps Cycle for the PREVIOUS machine from Flux Produits table
          const previousMachineFluxEntry = fluxProduits.find(fp =>
            fp.codeProduit === p.codeProduit &&
            (fp.machinesPossibles === previousMachine || fp.machinesPossibles?.includes(previousMachine))
          );

          const previousMachineSpecificCycle = previousMachineFluxEntry ?
            parseFloat(previousMachineFluxEntry.tempsCycle || previousMachineFluxEntry.TempsCycle || 0) :
            cycle; // fallback to current step cycle if not found

          const qtyOuvertePortion = qtyOuverte * previousMachineSpecificCycle;
          const timeToAdd = qtyOuvertePortion + estimatedSetup + transitTime;

          start = addMinutesConsideringSchedule(previousDebut, timeToAdd);
        }
      } else {
        // This is the first step of a product - ALWAYS use the product's own start date
        // Reset start to the product's individual start date, not continuing from previous product
        start = new Date(productStartDate);
      }



      // SELECT MACHINE (same as before, machines are not interchangeable)
      let machine;
      if (p.codeProduit === 'K170' && step.poste && step.poste.toLowerCase().includes('graissage')) {
        // Special case for K170 product to ensure it uses "Machine graissage 3"
        machine = machines.find(m => {
          const machineName = m.nomMachine || m.NomMachine || m.nom || m.Nom || m.name || m.Name;
          return machineName === 'Machine graissage 3';
        });

        if (machine) {
          // Normalize the machine object
          machine = {
            ...machine,
            nomMachine: 'Machine graissage 3',
            poste: step.poste,
            tempsSetup: machine.tempsSetup || machine.TempsSetup || machine.setupTime || machine.SetupTime || 0
          };
        } else {
          // Fallback to normal selection if Machine graissage 3 not found
          machine = getBestMachine(step.poste, step.machinesPossibles);
        }
      } else {
        // Normal case for other products
        machine = getBestMachine(step.poste, step.machinesPossibles);
      }

      // Use machine.nomMachine directly
      let machineName = machine?.nomMachine || 'N/A';

      // Special case for K170 product with graissage poste
      if (p.codeProduit === 'K170' && step.poste && step.poste.toLowerCase().includes('graissage')) {
        machineName = 'Machine graissage 3';
      }

      // Get actual setup time from selected machine
      const setupRaw = machine?.tempsSetup || machine?.TempsSetup || machine?.setupTime || machine?.SetupTime || '0';
      const setup = parseFloat(setupRaw);

      // Recalculate start time with actual machine if different from preliminary
      const preliminaryMachineName = preliminaryMachine?.nomMachine || preliminaryMachine?.NomMachine || preliminaryMachine?.nom || preliminaryMachine?.Nom || preliminaryMachine?.name || preliminaryMachine?.Name || 'N/A';
      if (machine && machineName !== preliminaryMachineName) {
        // Recalculate transit time with actual machine
        if (planning.length > 0 && !isFirstStepOfNewProduct) {
          const prev = planning[planning.length - 1];
          const previousMachine = prev.machine;
          const actualTransitTime = getTransitTime(previousMachine, machineName);

          if (qtyOuverte === 0) {
            // CASE 1: If Quantité Ouverte = 0
            const previousFin = new Date(prev.fin);
            start = addMinutesConsideringSchedule(previousFin, actualTransitTime);
          } else {
            // CASE 2: If Quantité Ouverte > 0
            const previousDebut = new Date(prev.debut);
            const previousMachineFluxEntry = fluxProduits.find(fp =>
              fp.codeProduit === p.codeProduit &&
              (fp.machinesPossibles === previousMachine || fp.machinesPossibles?.includes(previousMachine))
            );
            const previousMachineSpecificCycle = previousMachineFluxEntry ?
              parseFloat(previousMachineFluxEntry.tempsCycle || previousMachineFluxEntry.TempsCycle || 0) : cycle;
            const qtyOuvertePortion = qtyOuverte * previousMachineSpecificCycle;
            const timeToAdd = qtyOuvertePortion + setup + actualTransitTime;
            start = addMinutesConsideringSchedule(previousDebut, timeToAdd);
          }
        }
      }

      // CALCULATE ACTUAL TOTAL TIME WITH SELECTED MACHINE
      const totalTime = qty * cycle + setup;

      // CALCULATE ESTIMATED END TIME FOR CONFLICT CHECKING
      let estimatedEnd;
      if (qtyOuverte === 0) {
        estimatedEnd = addMinutesConsideringSchedule(start, totalTime, 0);
      } else {
        let estimatedResult;
        if (i === 0) {
          estimatedResult = totalTime;
        } else {
          const previousPlanningEntry = planning[planning.length - 1];
          const previousMachineName = previousPlanningEntry.machine;
          const previousMachineFluxEntry = fluxProduits.find(fp =>
            fp.codeProduit === p.codeProduit &&
            (fp.machinesPossibles === previousMachineName || fp.machinesPossibles?.includes(previousMachineName))
          );
          const previousMachineCycle = previousMachineFluxEntry ?
            parseFloat(previousMachineFluxEntry.tempsCycle || previousMachineFluxEntry.TempsCycle || 0) : 0;
          estimatedResult = totalTime + previousMachineResult - (qtyOuverte * previousMachineCycle);
        }
        estimatedEnd = addMinutesConsideringSchedule(start, estimatedResult, 0);
      }

      // CHECK FOR MACHINE CONFLICTS AND ADJUST START TIME IF NECESSARY
      if (isMachineBusy(machineName, start, estimatedEnd, planning)) {
        console.log(`⚠️ MACHINE CONFLICT DETECTED: ${machineName} is busy from ${start.toLocaleString()} to ${estimatedEnd.toLocaleString()}`);

        // Find when the machine becomes available
        const nextAvailableTime = findNextAvailableTime(machineName, start, planning);
        console.log(`⏰ MACHINE AVAILABLE AT: ${nextAvailableTime.toLocaleString()}, adjusting start time`);

        // Update start time to when machine becomes available
        start = nextAvailableTime;

        // If this is not the first step, we may need to recalculate based on the new start time
        // But we keep the same machine since machines are not interchangeable
      }

      console.log(`📊 BASIC CALC: ${p.codeProduit} ${step.poste} - Machine: ${machineName}, Qty: ${qty}, Cycle: ${cycle}min, Setup: ${setup}min, QtyOuverte: ${qtyOuverte}, TotalTime: ${totalTime}min`);

      // NEW FIN CALCULATION WITH TWO CASES
      let end;
      let finCalculationDetails = '';

      if (qtyOuverte === 0) {
        // CASE 1: If Quantité Ouverte = 0
        // Fin = current Début + Temps Total (min) from the Planning de Production table
        end = addMinutesConsideringSchedule(start, totalTime, 0);
        finCalculationDetails = `Case 1: Fin = Début + Temps Total = ${Math.round(totalTime * 10) / 10} min`;
        console.log(`⏰ FIN CALC CASE 1: ${p.codeProduit} ${step.poste} - Start: ${start.toLocaleString()}, Total: ${totalTime}min, End: ${end.toLocaleString()}`);
      } else {
        // CASE 2: If Quantité Ouverte > 0
        // Apply the recursive formula: Result[i] = Temps Total[i] + Result[i-1] - (Temps Cycle[i-1] × Quantité Ouverte)

        let currentMachineResult;

        if (i === 0) {
          // First machine: Result[0] = Temps Total[0]
          currentMachineResult = totalTime;
          finCalculationDetails = `Case 2 (First machine): Result = Temps Total = ${Math.round(totalTime * 10) / 10} min`;
          console.log(`🧮 CASE 2 FIRST: ${p.codeProduit} ${step.poste} - TotalTime: ${totalTime}min, Result: ${currentMachineResult}min`);
        } else {
          // Subsequent machines: Result[i] = Temps Total[i] + Result[i-1] - (Temps Cycle[i-1] × Quantité Ouverte)
          const previousPlanningEntry = planning[planning.length - 1];
          const previousMachineName = previousPlanningEntry.machine;

          const previousMachineFluxEntry = fluxProduits.find(fp =>
            fp.codeProduit === p.codeProduit &&
            (fp.machinesPossibles === previousMachineName || fp.machinesPossibles?.includes(previousMachineName))
          );

          const previousMachineCycle = previousMachineFluxEntry ?
            parseFloat(previousMachineFluxEntry.tempsCycle || previousMachineFluxEntry.TempsCycle || 0) :
            0;

          // Apply the recursive formula
          currentMachineResult = totalTime + previousMachineResult - (qtyOuverte * previousMachineCycle);
          finCalculationDetails = `Case 2: Result = ${Math.round(totalTime * 10) / 10} + ${Math.round(previousMachineResult * 10) / 10} - (${qtyOuverte} × ${Math.round(previousMachineCycle * 10) / 10}) = ${Math.round(currentMachineResult * 10) / 10} min`;
          console.log(`🧮 CASE 2 SUBSEQUENT: ${p.codeProduit} ${step.poste} - TotalTime: ${totalTime}min, PrevResult: ${previousMachineResult}min, QtyOuverte: ${qtyOuverte}, PrevCycle: ${previousMachineCycle}min, Result: ${currentMachineResult}min`);
        }

        // Fin = current Début + Result
        end = addMinutesConsideringSchedule(start, currentMachineResult, 0);
        finCalculationDetails += ` | Fin = Début + Result = ${Math.round(currentMachineResult * 10) / 10} min`;
        console.log(`⏰ FIN CALC CASE 2: ${p.codeProduit} ${step.poste} - Start: ${start.toLocaleString()}, Result: ${currentMachineResult}min, End: ${end.toLocaleString()}`);

        // Update previousMachineResult for next machine
        previousMachineResult = currentMachineResult;
      }

      // NOW SELECT OPERATOR: Get available operators for this machine, excluding those already working on the same day
      const operator = getBestAvailableOperator(machineName, start, planning);
      console.log(`👷 OPERATOR SELECTED for ${machineName}: ${operator.name} (experience: ${operator.minutesPerDay})`);

      // DEADLINE VALIDATION: Check if Fin exceeds the deadline
      const deadlineValue = p.deadline || p.Deadline || p.dateEcheance || p.DateEcheance || p.dateLimite || p.DateLimite;

      if (deadlineValue) {
        let deadline;
        try {
          // Parse deadline similar to start date
          if (deadlineValue instanceof Date) {
            deadline = new Date(deadlineValue);
          } else if (typeof deadlineValue === 'string' && deadlineValue.includes('T')) {
            deadline = new Date(deadlineValue);
          } else if (typeof deadlineValue === 'string' && deadlineValue.includes('/')) {
            const parts = deadlineValue.split('/');
            if (parts.length === 3) {
              deadline = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
              deadline.setHours(23, 59, 59, 999); // Set to end of day
            }
          } else {
            deadline = new Date(deadlineValue);
          }

          // Check if Fin exceeds deadline
          if (end > deadline) {
            // Adjust Fin to not exceed deadline
            end = new Date(deadline);
            finCalculationDetails += ` | ⚠️ ADJUSTED TO DEADLINE`;
          }
        } catch (e) {
          // Silently handle deadline parsing errors
        }
      }

      // Add to planning with properly formatted dates and detailed time information
      planning.push({
        id: `plan-${planning.length + 1}`,
        produit: p.codeProduit,
        poste: step.poste,
        machine: machineName, // Use the same machine name that was used for operator selection
        operateur: operator.name,
        debut: start.toISOString(), // Keep ISO format for internal use
        fin: end.toISOString(),     // Keep ISO format for internal use
        debutFormatted: formatDateTime(start), // Add formatted version for display
        finFormatted: formatDateTime(end),     // Add formatted version for display
        tempsTotal: Math.round(totalTime * 10) / 10, // Round to 1 decimal place - just cycle * quantity
        // Add detailed time breakdown for debugging
        tempsSetup: setup,
        tempsCyclePar: cycle,
        quantite: qty,
        quantiteOuverte: qtyOuverte,
        tempsCycleTotal: cycleTotal,
        // Include a formatted string showing the calculations
        tempsCalcul: `Temps Total = ${qty} × ${cycle} + ${setup} = ${Math.round(totalTime * 10) / 10} min | ${finCalculationDetails}`
      });

      // Note: Do NOT override start time here - the next step's start time
      // will be calculated based on Quantité Ouverte logic in the next iteration
    }
  }

  return planning;
};

export default generateCustomPlanning;