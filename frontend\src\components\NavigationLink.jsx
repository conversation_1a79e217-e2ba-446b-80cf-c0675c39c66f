import React from 'react';
import { Link } from 'react-router-dom';
import useUnsavedChangesWarning from '../hooks/useUnsavedChangesWarning';

/**
 * A custom Link component that checks for unsaved changes before navigating
 */
const NavigationLink = ({ to, children, ...props }) => {
  const { handleNavigation } = useUnsavedChangesWarning();

  const handleClick = (e) => {
    e.preventDefault();
    handleNavigation(to);
  };

  return (
    <Link to={to} onClick={handleClick} {...props}>
      {children}
    </Link>
  );
};

export default NavigationLink;
