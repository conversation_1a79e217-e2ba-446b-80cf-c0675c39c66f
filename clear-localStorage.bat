@echo off
echo Planification Michaud - Outil de nettoyage du stockage local
echo =========================================================
echo.
echo Cet outil va effacer les donnees stockees localement dans votre navigateur.
echo Utilisez-le uniquement si vous rencontrez des problemes avec l'application.
echo.
echo ATTENTION: Cela effacera toutes les donnees non sauvegardees dans le backend.
echo.
echo 1. Ouvrez l'application dans votre navigateur (http://localhost:5173)
echo 2. Appuyez sur F12 pour ouvrir les outils de developpement
echo 3. Allez dans l'onglet "Console"
echo 4. <PERSON><PERSON><PERSON> et collez la commande suivante:
echo.
echo    localStorage.removeItem('planificationMichaudData'); console.log('Stockage local efface!');
echo.
echo 5. Appuyez sur Entree
echo 6. Rafraichissez la page (F5)
echo.
echo L'application devrait maintenant charger les donnees depuis le backend.
echo.
pause
