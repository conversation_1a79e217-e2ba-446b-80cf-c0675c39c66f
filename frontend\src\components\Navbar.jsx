import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import NavigationLinkWithCheck from './NavigationLinkWithCheck';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Drawer,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Box,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import logoImage from '../assets/images/logo.png';

const navItems = [
  { name: 'Accueil', path: '/' },
  { name: 'Flux Produits', path: '/flux-produits' },
  { name: 'Machines', path: '/machines' },
  { name: 'Transit', path: '/transit' },
  { name: 'Ressources Humaines', path: '/ressources-humaines' },
  { name: 'Polyvalence', path: '/polyvalence' },
  { name: 'Ressources Matérielles', path: '/ressources-materielles' },
  { name: 'Stock MP', path: '/stock-mp' },
];

const Navbar = () => {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };

  const drawer = (
    <Box
      onClick={handleDrawerToggle}
      sx={{
        textAlign: 'center',
        height: '100%',
        background: 'linear-gradient(180deg, #0f172a 0%, #1e293b 100%)',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <Box sx={{
        my: 3,
        display: 'flex',
        justifyContent: 'center',
        position: 'relative',
        '&::after': {
          content: '""',
          position: 'absolute',
          bottom: -16,
          left: '20%',
          width: '60%',
          height: '1px',
          backgroundColor: 'rgba(203, 213, 225, 0.1)',
        }
      }}>
        <img
          src={logoImage}
          alt="Planification Michaud Logo"
          style={{
            height: '50px',
            filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))'
          }}
        />
      </Box>
      <List sx={{ width: '100%', pt: 2 }}>
        {navItems.map((item) => (
          <ListItem
            key={item.name}
            component={NavigationLinkWithCheck}
            to={item.path}
            disablePadding
            sx={{
              mb: 1,
              mx: 2,
              borderRadius: '8px',
              overflow: 'hidden',
            }}
          >
            <Box
              sx={{
                width: '100%',
                py: 1.5,
                px: 2,
                borderRadius: '8px',
                backgroundColor: location.pathname === item.path ? 'rgba(99, 102, 241, 0.15)' : 'transparent',
                color: location.pathname === item.path ? 'primary.light' : 'text.secondary',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  backgroundColor: 'rgba(99, 102, 241, 0.1)',
                  transform: 'translateX(5px)',
                },
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                position: 'relative',
                fontWeight: location.pathname === item.path ? 600 : 400,
              }}
            >
              <ListItemText
                primary={item.name}
                primaryTypographyProps={{
                  fontSize: '1rem',
                  fontWeight: location.pathname === item.path ? 600 : 400,
                }}
              />
              {location.pathname === item.path && (
                <Box
                  sx={{
                    position: 'absolute',
                    left: 0,
                    top: '25%',
                    height: '50%',
                    width: '3px',
                    backgroundColor: 'primary.main',
                    borderRadius: '0 3px 3px 0',
                  }}
                />
              )}
            </Box>
          </ListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <>
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          backdropFilter: 'blur(8px)',
          width: '100%',
          left: 0,
          right: 0,
        }}
      >
        <Toolbar sx={{ padding: { xs: '8px 16px', sm: '8px 24px' } }}>
          {isMobile && (
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{
                mr: 2,
                color: 'primary.light',
                '&:hover': {
                  backgroundColor: 'rgba(99, 102, 241, 0.1)',
                  transform: 'scale(1.05)',
                },
                transition: 'all 0.2s ease-in-out',
              }}
            >
              <MenuIcon />
            </IconButton>
          )}
          <Box
            sx={{
              flexGrow: 1,
              display: { xs: 'none', sm: 'flex' },
              alignItems: 'center'
            }}
          >
            <NavigationLinkWithCheck to="/" style={{ display: 'flex', alignItems: 'center' }}>
              <img
                src={logoImage}
                alt="Planification Michaud Logo"
                style={{
                  height: '40px',
                  marginRight: '10px',
                  transition: 'transform 0.3s ease',
                  filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))'
                }}
                className="hover-logo"
              />
            </NavigationLinkWithCheck>
          </Box>
          <Box
            sx={{
              display: { xs: 'none', md: 'flex' },
              gap: '4px'
            }}
          >
            {navItems.map((item) => (
              <Button
                key={item.name}
                component={NavigationLinkWithCheck}
                to={item.path}
                sx={{
                  color: location.pathname === item.path ? 'primary.light' : 'text.secondary',
                  backgroundColor: location.pathname === item.path ? 'rgba(99, 102, 241, 0.15)' : 'transparent',
                  borderRadius: '8px',
                  padding: '8px 16px',
                  fontWeight: location.pathname === item.path ? 600 : 500,
                  position: 'relative',
                  overflow: 'hidden',
                  '&:hover': {
                    backgroundColor: 'rgba(99, 102, 241, 0.1)',
                    color: 'primary.light',
                    transform: 'translateY(-2px)',
                  },
                  '&::after': location.pathname === item.path ? {
                    content: '""',
                    position: 'absolute',
                    bottom: 0,
                    left: '20%',
                    width: '60%',
                    height: '3px',
                    backgroundColor: 'primary.main',
                    borderRadius: '3px 3px 0 0',
                  } : {},
                  transition: 'all 0.2s ease-in-out',
                  mx: 0.5
                }}
              >
                {item.name}
              </Button>
            ))}
          </Box>
        </Toolbar>
      </AppBar>
      <Drawer
        variant="temporary"
        open={drawerOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: 280,
            borderRight: 'none',
            boxShadow: '4px 0 15px rgba(0, 0, 0, 0.2)',
          },
          '& .MuiBackdrop-root': {
            backgroundColor: 'rgba(15, 23, 42, 0.7)',
            backdropFilter: 'blur(4px)',
          },
        }}
      >
        {drawer}
      </Drawer>
    </>
  );
};

export default Navbar;
