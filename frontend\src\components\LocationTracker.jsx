import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

// List of navbar table paths
const navbarTablePaths = [
  '/flux-produits',
  '/machines',
  '/transit',
  '/ressources-humaines',
  '/polyvalence',
  '/ressources-materielles',
  '/stock-mp',
];

// List of allowed previous pages for navbar tables
const allowedPreviousPages = [
  '/', // Home
  '/planning-results', // Résultats de Planification (Excel or Manual)
];

/**
 * Component that tracks location changes and updates sessionStorage
 * to help with navigation between pages
 */
const LocationTracker = () => {
  const location = useLocation();

  useEffect(() => {
    const currentPath = location.pathname;

    // Only update previous path if it's an allowed previous page
    // This ensures back buttons on navbar tables only go to Home or Planning Results
    if (allowedPreviousPages.includes(currentPath)) {
      // Store the path
      sessionStorage.setItem('previousPath', currentPath);

      // If we're on the planning-results page, also store the entry method
      if (currentPath === '/planning-results' && location.state?.entryMethod) {
        sessionStorage.setItem('previousEntryMethod', location.state.entryMethod);
        console.log('Updated previousEntryMethod in sessionStorage:', location.state.entryMethod);
      }

      console.log('Updated previousPath in sessionStorage:', currentPath);
    }
  }, [location.pathname, location.state]);

  // This component doesn't render anything
  return null;
};

export default LocationTracker;
