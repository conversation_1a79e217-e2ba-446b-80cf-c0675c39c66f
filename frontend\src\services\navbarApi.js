import { fluxProduits, machines, transit, ressourcesHumaines, polyvalence, ressourcesMaterielles as ressourcesMatAPI, stockMP } from './api.js';

// Track unsaved changes for navbar tables
let hasUnsavedChanges = false;

// Working data for navbar tables
const workingData = {
  fluxProduits: [],
  machines: [],
  transit: [],
  ressourcesHumaines: [],
  polyvalence: [],
  ressourcesMaterielles: [],
  stockMP: []
};

// Track if data has been loaded from backend
const dataLoaded = {
  fluxProduits: false,
  machines: false,
  transit: false,
  ressourcesHumaines: false,
  polyvalence: false,
  ressourcesMaterielles: false,
  stockMP: false
};

// Cache for loading promises to avoid duplicate API calls
const loadingPromises = {};

// Set unsaved changes flag
export const setUnsavedChanges = (value) => {
  hasUnsavedChanges = value;
};

// Check if there are unsaved changes
export const checkUnsavedChanges = () => hasUnsavedChanges;

// Preload data for a specific entity
const preloadEntity = async (entityName, apiService) => {
  if (dataLoaded[entityName] || loadingPromises[entityName]) {
    return;
  }

  try {
    loadingPromises[entityName] = apiService.getAll();
    const data = await loadingPromises[entityName];
    workingData[entityName] = data;
    dataLoaded[entityName] = true;
  } catch (error) {
    // Silently fail for preloading
  } finally {
    delete loadingPromises[entityName];
  }
};

// Preload all navbar data in the background
export const preloadAllNavbarData = async () => {
  const entities = [
    { name: 'fluxProduits', service: fluxProduits },
    { name: 'machines', service: machines },
    { name: 'transit', service: transit },
    { name: 'ressourcesHumaines', service: ressourcesHumaines },
    { name: 'polyvalence', service: polyvalence },
    { name: 'ressourcesMaterielles', service: ressourcesMatAPI },
    { name: 'stockMP', service: stockMP }
  ];

  // Start loading all entities in parallel
  const promises = entities.map(({ name, service }) => preloadEntity(name, service));
  await Promise.allSettled(promises);
};

// Discard changes
export const discardChanges = () => {
  hasUnsavedChanges = false;
  // Reset working data
  Object.keys(workingData).forEach(key => {
    workingData[key] = [];
  });
};

// Save all data to backend
export const saveAllData = async () => {
  try {
    const errors = [];

    // Save each entity's working data to backend

    for (const [entityName, data] of Object.entries(workingData)) {
      if (data.length > 0) {
        try {
          // Get the corresponding API service
          let apiService;
          switch (entityName) {
            case 'fluxProduits':
              apiService = fluxProduits;
              break;
            case 'machines':
              apiService = machines;
              break;
            case 'transit':
              apiService = transit;
              break;
            case 'ressourcesHumaines':
              apiService = ressourcesHumaines;
              break;
            case 'polyvalence':
              apiService = polyvalence;
              break;
            case 'ressourcesMaterielles':
              apiService = ressourcesMatAPI;
              break;
            case 'stockMP':
              apiService = stockMP;
              break;
            default:
              continue;
          }

          // Replace the entire dataset in the backend
          const result = await apiService.replaceAll(data);
        } catch (error) {
          errors.push(entityName);
        }
      }
    }

    // Reset the unsaved changes flag
    hasUnsavedChanges = false;

    if (errors.length > 0) {
      return {
        success: false,
        message: `Erreurs lors de la sauvegarde de: ${errors.join(', ')}`
      };
    }

    return { success: true, message: 'Changements sauvegardés avec succès' };
  } catch (error) {
    return { success: false, message: 'Erreur lors de la sauvegarde' };
  }
};

// Create CRUD operations for navbar tables
const createNavbarCRUD = (entityName, apiService) => ({
  getAll: async (forceRefresh = false) => {
    try {
      // Return cached data immediately if available and not forcing refresh
      if (!forceRefresh && dataLoaded[entityName] && workingData[entityName].length > 0) {
        return [...workingData[entityName]];
      }

      // If already loading, wait for the existing promise
      if (loadingPromises[entityName]) {
        await loadingPromises[entityName];
        return [...workingData[entityName]];
      }

      // Start loading data
      loadingPromises[entityName] = apiService.getAll();
      const data = await loadingPromises[entityName];

      workingData[entityName] = data;
      dataLoaded[entityName] = true;
      delete loadingPromises[entityName];

      return [...data];
    } catch (error) {
      delete loadingPromises[entityName];
      return workingData[entityName] || [];
    }
  },

  add: async (item) => {
    try {
      const newItem = { ...item, id: item.id || Date.now().toString() };
      workingData[entityName].push(newItem);
      hasUnsavedChanges = true;
      return newItem;
    } catch (error) {
      throw error;
    }
  },

  update: async (item) => {
    try {
      const index = workingData[entityName].findIndex(i => i.id === item.id);
      if (index !== -1) {
        workingData[entityName][index] = item;
        hasUnsavedChanges = true;
      }
      return item;
    } catch (error) {
      throw error;
    }
  },

  delete: async (id) => {
    try {
      const index = workingData[entityName].findIndex(i => i.id === id);
      if (index !== -1) {
        workingData[entityName].splice(index, 1);
        hasUnsavedChanges = true;
      }
      return { success: true };
    } catch (error) {
      throw error;
    }
  }
});

// Export navbar table services
export const fluxProduitsService = createNavbarCRUD('fluxProduits', fluxProduits);
export const machinesService = createNavbarCRUD('machines', machines);
export const transitService = createNavbarCRUD('transit', transit);
export const ressourcesHumainesService = createNavbarCRUD('ressourcesHumaines', ressourcesHumaines);
export const polyvalenceService = createNavbarCRUD('polyvalence', polyvalence);
export const ressourcesMaterielles = createNavbarCRUD('ressourcesMaterielles', ressourcesMatAPI);
export const stockMPService = createNavbarCRUD('stockMP', stockMP);

// Legacy exports for compatibility
export {
  fluxProduitsService as fluxProduits,
  machinesService as machines,
  transitService as transit,
  ressourcesHumainesService as ressourcesHumaines,
  polyvalenceService as polyvalence,
  stockMPService as stockMP
};
