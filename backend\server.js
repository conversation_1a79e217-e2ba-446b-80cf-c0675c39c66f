const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const multer = require('multer');
const xlsx = require('xlsx');
const fs = require('fs');
const path = require('path');

console.log('Modules loaded successfully');

// Ensure data directory exists
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
  console.log('Created data directory');
}

// Initialize data files if they don't exist
const dataFiles = [
  'fluxProduits.json',
  'machines.json',
  'transit.json',
  'ressourcesHumaines.json',
  'polyvalence.json',
  'ressourcesMaterielles.json',
  'produits.json',
  'stockMP.json'
];

dataFiles.forEach(file => {
  const filePath = path.join(dataDir, file);
  if (!fs.existsSync(filePath)) {
    fs.writeFileSync(filePath, JSON.stringify([]));
    console.log(`Created empty data file: ${file}`);
  }
});

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Set up storage for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(__dirname, 'uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir);
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    cb(null, `${Date.now()}-${file.originalname}`);
  }
});

const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.mimetype === 'application/vnd.ms-excel') {
      cb(null, true);
    } else {
      cb(new Error('Only Excel files are allowed!'), false);
    }
  }
});

// Data storage (in-memory for simplicity)
let fluxProduits = [];
let machines = [];
let transit = [];
let ressourcesHumaines = [];
let polyvalence = [];
let ressourcesMaterielles = [];
let stockMP = [];
// Produits removed - handled independently in frontend
// Note: productionPlanning removed - planning is generated dynamically, not stored

// Helper function to save data to JSON files
const saveDataToFile = (data, filename) => {
  const filePath = path.join(__dirname, 'data', filename);
  const dirPath = path.join(__dirname, 'data');

  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }

  fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
};

// Helper function to load data from JSON files
const loadDataFromFile = (filename) => {
  console.log(`Loading file: ${filename}`);
  const filePath = path.join(__dirname, 'data', filename);
  console.log(`Full path: ${filePath}`);

  if (fs.existsSync(filePath)) {
    try {
      console.log(`File ${filename} exists, reading...`);
      const data = fs.readFileSync(filePath, 'utf8');
      console.log(`Read ${data.length} characters from ${filename}`);

      // Check if the file is empty or contains only whitespace
      if (!data.trim()) {
        console.log(`File ${filename} is empty, creating with empty array`);
        const emptyArray = [];
        fs.writeFileSync(filePath, JSON.stringify(emptyArray, null, 2), 'utf8');
        return emptyArray;
      }

      const parsed = JSON.parse(data);
      return parsed;
    } catch (error) {
      const emptyArray = [];
      fs.writeFileSync(filePath, JSON.stringify(emptyArray, null, 2), 'utf8');
      return emptyArray;
    }
  } else {
    const emptyArray = [];
    fs.writeFileSync(filePath, JSON.stringify(emptyArray, null, 2), 'utf8');
    return emptyArray;
  }
};

// Load data on server start
try {


  fluxProduits = loadDataFromFile('fluxProduits.json');
  machines = loadDataFromFile('machines.json');
  transit = loadDataFromFile('transit.json');
  ressourcesHumaines = loadDataFromFile('ressourcesHumaines.json');
  polyvalence = loadDataFromFile('polyvalence.json');
  ressourcesMaterielles = loadDataFromFile('ressourcesMaterielles.json');
  stockMP = loadDataFromFile('stockMP.json');

  // Produits loading removed - handled independently in frontend

  // If data files are empty, populate with sample data
  if (machines.length === 0) {
    machines = [
      {"nomMachine": "Machine BA 600", "poste": "Surmoulage", "capacite": 950, "tempsSetup": 60, "disponibilite": 1, "id": "1747490214320"},
      {"nomMachine": "Machine graissage 3", "poste": "Graissage", "capacite": 3800, "tempsSetup": 7, "disponibilite": 1, "id": "1747490214321"},
      {"nomMachine": "Machine soudage 2", "poste": "Soudage", "capacite": 2200, "tempsSetup": 10, "disponibilite": 1, "id": "1747490214322"},
      {"nomMachine": "Machine BA 800", "poste": "Surmoulage", "capacite": 1200, "tempsSetup": 60, "disponibilite": 1, "id": "1747490214323"},
      {"nomMachine": "Machine graissage 1", "poste": "Graissage", "capacite": 2800, "tempsSetup": 3, "disponibilite": 1, "id": "1747490214324"},
      {"nomMachine": "Machine soudage 1", "poste": "Soudage", "capacite": 1800, "tempsSetup": 10, "disponibilite": 1, "id": "1747490214325"},
      {"nomMachine": "Machine BA 1000", "poste": "Surmoulage", "capacite": 1500, "tempsSetup": 60, "disponibilite": 1, "id": "1747490214326"},
      {"nomMachine": "Machine graissage 2", "poste": "Graissage", "capacite": 3200, "tempsSetup": 5, "disponibilite": 1, "id": "1747490214327"},
      {"nomMachine": "Machine soudage 3", "poste": "Soudage", "capacite": 2500, "tempsSetup": 10, "disponibilite": 1, "id": "1747490214328"},
      {"nomMachine": "Machine BA 1200", "poste": "Surmoulage", "capacite": 1800, "tempsSetup": 60, "disponibilite": 1, "id": "1747490214329"}
    ];
  }

  // Only populate with sample data if the file is empty
  if (fluxProduits.length === 0) {
    fluxProduits = [
      {"codeProduit": "K076", "ordre": 1, "poste": "Surmoulage", "machinesPossibles": "Machine BA 600, Machine BA 800", "tempsCycle": 0.2, "composant": "Corps principal", "id": "1747490214330"},
      {"codeProduit": "K076", "ordre": 2, "poste": "Graissage", "machinesPossibles": "Machine graissage 1, Machine graissage 2", "tempsCycle": 0.12, "composant": "Graisse", "id": "1747490214331"},
      {"codeProduit": "K076", "ordre": 3, "poste": "Soudage", "machinesPossibles": "Machine soudage 1, Machine soudage 2", "tempsCycle": 0.15, "composant": "Soudure", "id": "1747490214332"},
      {"codeProduit": "K076", "ordre": 4, "poste": "Assemblage", "machinesPossibles": "Manuel", "tempsCycle": 0.3, "composant": "Assemblage final", "id": "1747490214333"},
      {"codeProduit": "K170", "ordre": 1, "poste": "Surmoulage", "machinesPossibles": "Machine BA 1000, Machine BA 1200", "tempsCycle": 0.6, "composant": "Corps principal", "id": "1747490214334"},
      {"codeProduit": "K170", "ordre": 2, "poste": "Graissage", "machinesPossibles": "Machine graissage 2, Machine graissage 3", "tempsCycle": 0.15, "composant": "Graisse", "id": "1747490214335"},
      {"codeProduit": "K170", "ordre": 3, "poste": "Soudage", "machinesPossibles": "Machine soudage 2, Machine soudage 3", "tempsCycle": 0.2, "composant": "Soudure", "id": "1747490214336"},
      {"codeProduit": "K170", "ordre": 4, "poste": "Assemblage", "machinesPossibles": "Manuel", "tempsCycle": 0.3, "composant": "Assemblage final", "id": "1747490214337"},
      {"codeProduit": "K200", "ordre": 1, "poste": "Surmoulage", "machinesPossibles": "Machine BA 600", "tempsCycle": 0.8, "composant": "Corps principal", "id": "1747490214338"},
      {"codeProduit": "K200", "ordre": 2, "poste": "Assemblage", "machinesPossibles": "Manuel", "tempsCycle": 0.4, "composant": "Assemblage final", "id": "1747490214339"}
    ];
  }

} catch (error) {
  // Error loading data - continue with empty arrays
}

// Helper function to synchronize Stock MP with Ressources Matérielles
const synchronizeStockMPWithRessourcesMaterielles = () => {
  // Create a map of existing stock MP items for quick lookup
  const existingStockMap = new Map();
  stockMP.forEach(item => {
    const key = `${item.codeProduit}-${item.composant}`;
    existingStockMap.set(key, item);
  });

  // Create synchronized stock MP array
  const synchronizedStock = [];

  // Add all items from Ressources Matérielles
  ressourcesMaterielles.forEach(rm => {
    const key = `${rm.codeProduit}-${rm.composant}`;
    const existingItem = existingStockMap.get(key);

    if (existingItem) {
      // Item exists in stock MP, keep the existing quantity
      synchronizedStock.push({
        id: existingItem.id,
        codeProduit: rm.codeProduit,
        composant: rm.composant,
        designation: rm.designation,
        quantiteEnStock: existingItem.quantiteEnStock
      });
    } else {
      // New item from Ressources Matérielles, add with quantity 0
      synchronizedStock.push({
        id: `${rm.codeProduit}-${rm.composant}-${Date.now()}`,
        codeProduit: rm.codeProduit,
        composant: rm.composant,
        designation: rm.designation,
        quantiteEnStock: 0
      });
    }
  });

  // Update the stockMP array with synchronized data
  stockMP.length = 0; // Clear existing array
  stockMP.push(...synchronizedStock);

  // Save the synchronized data to file
  saveDataToFile(stockMP, 'stockMP.json');

  return synchronizedStock;
};

// API Routes
// Test endpoint
app.get('/api/test', (req, res) => {
  res.json({ message: 'Server is working', machinesCount: machines.length });
});

// Flux Produits
app.get('/api/flux-produits', (req, res) => {
  res.json(fluxProduits);
});

app.post('/api/flux-produits', (req, res) => {
  const newItem = req.body;
  fluxProduits.push(newItem);
  saveDataToFile(fluxProduits, 'fluxProduits.json');
  res.status(201).json(newItem);
});

app.put('/api/flux-produits/replace-all', (req, res) => {
  try {
    const newData = req.body;
    fluxProduits.length = 0; // Clear existing array
    fluxProduits.push(...newData); // Add new data
    saveDataToFile(fluxProduits, 'fluxProduits.json');
    res.json({ message: 'Data replaced successfully', count: newData.length });
  } catch (error) {
    res.status(500).json({ message: 'Error replacing data' });
  }
});

app.put('/api/flux-produits/:id', (req, res) => {
  const { id } = req.params;
  const updatedItem = req.body;

  const index = fluxProduits.findIndex(item => item.id === id);
  if (index !== -1) {
    fluxProduits[index] = updatedItem;
    saveDataToFile(fluxProduits, 'fluxProduits.json');
    res.json(updatedItem);
  } else {
    res.status(404).json({ message: 'Item not found' });
  }
});

app.delete('/api/flux-produits/:id', (req, res) => {
  const { id } = req.params;

  const index = fluxProduits.findIndex(item => item.id === id);
  if (index !== -1) {
    fluxProduits.splice(index, 1);
    saveDataToFile(fluxProduits, 'fluxProduits.json');
    res.json({ message: 'Item deleted successfully' });
  } else {
    res.status(404).json({ message: 'Item not found' });
  }
});

// Machines
app.get('/api/machines', (req, res) => {
  res.json(machines);
});

app.post('/api/machines', (req, res) => {
  const newItem = req.body;
  machines.push(newItem);
  saveDataToFile(machines, 'machines.json');
  res.status(201).json(newItem);
});

app.put('/api/machines/replace-all', (req, res) => {
  try {
    const newData = req.body;
    machines.length = 0;
    machines.push(...newData);
    saveDataToFile(machines, 'machines.json');
    res.json({ message: 'Data replaced successfully', count: newData.length });
  } catch (error) {
    res.status(500).json({ message: 'Error replacing data' });
  }
});

app.put('/api/machines/:id', (req, res) => {
  const { id } = req.params;
  const updatedItem = req.body;

  const index = machines.findIndex(item => item.id === id);
  if (index !== -1) {
    machines[index] = updatedItem;
    saveDataToFile(machines, 'machines.json');
    res.json(updatedItem);
  } else {
    res.status(404).json({ message: 'Item not found' });
  }
});

app.delete('/api/machines/:id', (req, res) => {
  const { id } = req.params;

  const index = machines.findIndex(item => item.id === id);
  if (index !== -1) {
    machines.splice(index, 1);
    saveDataToFile(machines, 'machines.json');
    res.json({ message: 'Item deleted successfully' });
  } else {
    res.status(404).json({ message: 'Item not found' });
  }
});

// Transit
app.get('/api/transit', (req, res) => {
  res.json(transit);
});

app.post('/api/transit', (req, res) => {
  const newItem = req.body;
  transit.push(newItem);
  saveDataToFile(transit, 'transit.json');
  res.status(201).json(newItem);
});

app.put('/api/transit/replace-all', (req, res) => {
  try {
    const newData = req.body;
    transit.length = 0;
    transit.push(...newData);
    saveDataToFile(transit, 'transit.json');
    res.json({ message: 'Data replaced successfully', count: newData.length });
  } catch (error) {
    res.status(500).json({ message: 'Error replacing data' });
  }
});

app.put('/api/transit/:id', (req, res) => {
  const { id } = req.params;
  const updatedItem = req.body;

  const index = transit.findIndex(item => item.id === id);
  if (index !== -1) {
    transit[index] = updatedItem;
    saveDataToFile(transit, 'transit.json');
    res.json(updatedItem);
  } else {
    res.status(404).json({ message: 'Item not found' });
  }
});

app.delete('/api/transit/:id', (req, res) => {
  const { id } = req.params;

  const index = transit.findIndex(item => item.id === id);
  if (index !== -1) {
    transit.splice(index, 1);
    saveDataToFile(transit, 'transit.json');
    res.json({ message: 'Item deleted successfully' });
  } else {
    res.status(404).json({ message: 'Item not found' });
  }
});

// Ressources Humaines
app.get('/api/ressources-humaines', (req, res) => {
  res.json(ressourcesHumaines);
});

app.post('/api/ressources-humaines', (req, res) => {
  const newItem = req.body;
  ressourcesHumaines.push(newItem);
  saveDataToFile(ressourcesHumaines, 'ressourcesHumaines.json');
  res.status(201).json(newItem);
});

app.put('/api/ressources-humaines/replace-all', (req, res) => {
  try {
    const newData = req.body;
    ressourcesHumaines.length = 0;
    ressourcesHumaines.push(...newData);
    saveDataToFile(ressourcesHumaines, 'ressourcesHumaines.json');
    res.json({ message: 'Data replaced successfully', count: newData.length });
  } catch (error) {
    res.status(500).json({ message: 'Error replacing data' });
  }
});

app.put('/api/ressources-humaines/:id', (req, res) => {
  const { id } = req.params;
  const updatedItem = req.body;

  const index = ressourcesHumaines.findIndex(item => item.id === id);
  if (index !== -1) {
    ressourcesHumaines[index] = updatedItem;
    saveDataToFile(ressourcesHumaines, 'ressourcesHumaines.json');
    res.json(updatedItem);
  } else {
    res.status(404).json({ message: 'Item not found' });
  }
});

app.delete('/api/ressources-humaines/:id', (req, res) => {
  const { id } = req.params;

  const index = ressourcesHumaines.findIndex(item => item.id === id);
  if (index !== -1) {
    ressourcesHumaines.splice(index, 1);
    saveDataToFile(ressourcesHumaines, 'ressourcesHumaines.json');
    res.json({ message: 'Item deleted successfully' });
  } else {
    res.status(404).json({ message: 'Item not found' });
  }
});

// Polyvalence
app.get('/api/polyvalence', (req, res) => {
  res.json(polyvalence);
});

app.post('/api/polyvalence', (req, res) => {
  const newItem = req.body;
  polyvalence.push(newItem);
  saveDataToFile(polyvalence, 'polyvalence.json');
  res.status(201).json(newItem);
});

app.put('/api/polyvalence/replace-all', (req, res) => {
  try {
    const newData = req.body;
    polyvalence.length = 0;
    polyvalence.push(...newData);
    saveDataToFile(polyvalence, 'polyvalence.json');
    res.json({ message: 'Data replaced successfully', count: newData.length });
  } catch (error) {
    res.status(500).json({ message: 'Error replacing data' });
  }
});

app.put('/api/polyvalence/:id', (req, res) => {
  const { id } = req.params;
  const updatedItem = req.body;

  const index = polyvalence.findIndex(item => item.id === id);
  if (index !== -1) {
    polyvalence[index] = updatedItem;
    saveDataToFile(polyvalence, 'polyvalence.json');
    res.json(updatedItem);
  } else {
    res.status(404).json({ message: 'Item not found' });
  }
});

app.delete('/api/polyvalence/:id', (req, res) => {
  const { id } = req.params;

  const index = polyvalence.findIndex(item => item.id === id);
  if (index !== -1) {
    polyvalence.splice(index, 1);
    saveDataToFile(polyvalence, 'polyvalence.json');
    res.json({ message: 'Item deleted successfully' });
  } else {
    res.status(404).json({ message: 'Item not found' });
  }
});

// Ressources Materielles
app.get('/api/ressources-materielles', (req, res) => {
  res.json(ressourcesMaterielles);
});

app.post('/api/ressources-materielles', (req, res) => {
  const newItem = req.body;
  ressourcesMaterielles.push(newItem);
  saveDataToFile(ressourcesMaterielles, 'ressourcesMaterielles.json');
  res.status(201).json(newItem);
});

app.put('/api/ressources-materielles/replace-all', (req, res) => {
  try {
    const newData = req.body;
    ressourcesMaterielles.length = 0;
    ressourcesMaterielles.push(...newData);
    saveDataToFile(ressourcesMaterielles, 'ressourcesMaterielles.json');

    // Synchronize Stock MP after replacing Ressources Matérielles
    synchronizeStockMPWithRessourcesMaterielles();

    res.json({ message: 'Data replaced successfully', count: newData.length });
  } catch (error) {
    res.status(500).json({ message: 'Error replacing data' });
  }
});

app.put('/api/ressources-materielles/:id', (req, res) => {
  const { id } = req.params;
  const updatedItem = req.body;

  const index = ressourcesMaterielles.findIndex(item => item.id === id);
  if (index !== -1) {
    ressourcesMaterielles[index] = updatedItem;
    saveDataToFile(ressourcesMaterielles, 'ressourcesMaterielles.json');
    res.json(updatedItem);
  } else {
    res.status(404).json({ message: 'Item not found' });
  }
});

app.delete('/api/ressources-materielles/:id', (req, res) => {
  const { id } = req.params;

  const index = ressourcesMaterielles.findIndex(item => item.id === id);
  if (index !== -1) {
    ressourcesMaterielles.splice(index, 1);
    saveDataToFile(ressourcesMaterielles, 'ressourcesMaterielles.json');
    res.json({ message: 'Item deleted successfully' });
  } else {
    res.status(404).json({ message: 'Item not found' });
  }
});

// Stock MP
app.get('/api/stock-mp', (req, res) => {
  // Synchronize Stock MP with Ressources Matérielles
  const synchronizedStockMP = synchronizeStockMPWithRessourcesMaterielles();
  res.json(synchronizedStockMP);
});

app.post('/api/stock-mp', (req, res) => {
  // For Stock MP, we don't allow manual addition since items come from Ressources Matérielles
  // Instead, we synchronize and return the updated list
  const synchronizedStockMP = synchronizeStockMPWithRessourcesMaterielles();
  res.status(200).json({ message: 'Stock MP synchronized with Ressources Matérielles', data: synchronizedStockMP });
});

app.put('/api/stock-mp/replace-all', (req, res) => {
  try {
    const newData = req.body;
    stockMP.length = 0;
    stockMP.push(...newData);
    saveDataToFile(stockMP, 'stockMP.json');
    res.json({ message: 'Data replaced successfully', count: newData.length });
  } catch (error) {
    res.status(500).json({ message: 'Error replacing data' });
  }
});

app.put('/api/stock-mp/:id', (req, res) => {
  const { id } = req.params;
  const updatedItem = req.body;

  // First synchronize to ensure we have the latest data
  synchronizeStockMPWithRessourcesMaterielles();

  // Find and update the item (only quantiteEnStock should be editable)
  const index = stockMP.findIndex(item => item.id === id);
  if (index !== -1) {
    // Only update the quantiteEnStock field, keep other fields from Ressources Matérielles
    stockMP[index].quantiteEnStock = parseFloat(updatedItem.quantiteEnStock) || 0;
    saveDataToFile(stockMP, 'stockMP.json');
    res.json(stockMP[index]);
  } else {
    res.status(404).json({ message: 'Item not found' });
  }
});

app.delete('/api/stock-mp/:id', (req, res) => {
  // For Stock MP, we don't allow manual deletion since items come from Ressources Matérielles
  // Items are automatically removed when they're removed from Ressources Matérielles
  res.status(400).json({ message: 'Cannot delete Stock MP items manually. Items are synchronized with Ressources Matérielles.' });
});

// Produits endpoints removed - Produits tables work independently without backend interaction

// Production Planning endpoint removed - planning is generated dynamically, not stored

// Planning generation endpoints removed - all planning is now generated on frontend only
// This ensures planning tables never load from or save to backend as required



// Planning de Matérielles API removed - planning is generated dynamically on frontend, not stored

// Excel File Upload
app.post('/api/upload-excel', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }

    const filePath = req.file.path;

    try {
      const workbook = xlsx.readFile(filePath);

      // Check if the Produits sheet exists
      if (!workbook.SheetNames.includes('Produits')) {
        return res.status(400).json({
          message: 'Excel file must contain a sheet named "Produits" (case-sensitive). Available sheets: ' + workbook.SheetNames.join(', ')
        });
      }

      const worksheet = workbook.Sheets['Produits'];

      // Use options to properly parse dates
      const data = xlsx.utils.sheet_to_json(worksheet, {
        raw: false,
        dateNF: 'dd/mm/yyyy', // Set date format
        cellDates: true // Parse dates as Date objects
      });

      if (data.length === 0) {
        return res.status(400).json({ message: 'No data found in the Produits sheet' });
      }

      // Validate the data structure
      const requiredColumns = [
        { name: 'CodeProduit', alternatives: [] },
        { name: 'QuantitéDemandée', alternatives: ['QuantiteDemandee'] },
        { name: 'Deadline', alternatives: [] },
        { name: 'Priorité', alternatives: ['Priorite'] },
        { name: 'QuantitéOuverte', alternatives: ['QuantiteOuverte', 'Quantité Ouverte', 'QuantitéeOuverte'] },
        { name: 'StartDate', alternatives: [] },
        { name: 'TempsCycleTotal', alternatives: ['TempsCycleTotal (min)'] }
      ];

      const availableColumns = Object.keys(data[0] || {});

      const missingColumns = [];

      for (const col of requiredColumns) {
        const mainName = col.name;
        const alternatives = col.alternatives;

        // Check if either the main name or any alternative is present
        const found = availableColumns.some(availCol =>
          availCol === mainName || alternatives.includes(availCol)
        );

        if (!found) {
          missingColumns.push(mainName);
        }
      }

      if (missingColumns.length > 0) {
        return res.status(400).json({
          message: `Excel file is missing required columns: ${missingColumns.join(', ')}`
        });
      }

      // Map column names to standardized names
      const columnMapping = {};
      requiredColumns.forEach(col => {
        const mainName = col.name;
        const alternatives = col.alternatives;

        // Find which version of the column name is present
        const foundColumn = availableColumns.find(availCol =>
          availCol === mainName || alternatives.includes(availCol)
        );

        if (foundColumn) {
          columnMapping[mainName] = foundColumn;
        }
      });



      // Process and format the data
      const formattedData = data.map(item => {
        // Generate a unique ID
        const id = Date.now().toString() + Math.random().toString(36).substring(2, 7);

        // Use the column mapping to get the correct field names
        const codeProduit = item[columnMapping['CodeProduit']];
        const quantiteDemandee = parseInt(item[columnMapping['QuantitéDemandée']]);

        // Parse Quantité Ouverte - keep the actual value from Excel, including 0
        let quantiteeOuverte = 1; // Default to 1 only if no value provided
        const quantiteeOuverteRaw = item[columnMapping['QuantitéOuverte']];

        // Handle Quantité Ouverte - keep actual values including 0
        if (quantiteeOuverteRaw !== undefined && quantiteeOuverteRaw !== null && quantiteeOuverteRaw !== '') {
          const parsedValue = parseInt(quantiteeOuverteRaw);
          if (!isNaN(parsedValue)) {
            quantiteeOuverte = parsedValue; // Keep the actual value, including 0
          }
        }



        // Process date fields
        let deadline = item[columnMapping['Deadline']];
        let startDate = item[columnMapping['StartDate']];

        // Format dates properly
        if (deadline) {
          // Check if deadline is already a Date object
          if (deadline instanceof Date) {
            deadline = deadline.toISOString().split('T')[0]; // Format as YYYY-MM-DD
          } else if (typeof deadline === 'string') {
            // Try to parse the date string
            const parts = deadline.split('/');
            if (parts.length === 3) {
              // Assuming format is MM/DD/YY or DD/MM/YY
              let day, month, year;

              // Try to determine the format based on values
              if (parseInt(parts[0]) > 12) {
                // First part is likely the day
                day = parseInt(parts[0]);
                month = parseInt(parts[1]);
              } else {
                // First part could be month or day, assume MM/DD format
                month = parseInt(parts[0]);
                day = parseInt(parts[1]);
              }

              year = parseInt(parts[2]);
              // Handle 2-digit years
              if (year < 100) {
                year += year < 50 ? 2000 : 1900;
              }

              // Use UTC to avoid timezone issues
              const dateObj = new Date(Date.UTC(year, month - 1, day));
              if (!isNaN(dateObj.getTime())) {
                deadline = dateObj.toISOString().split('T')[0]; // Format as YYYY-MM-DD
              }
            }
          }
        }

        if (startDate) {
          // Check if startDate is already a Date object
          if (startDate instanceof Date) {
            startDate = startDate.toISOString().split('T')[0]; // Format as YYYY-MM-DD
          } else if (typeof startDate === 'string') {
            // Try to parse the date string
            const parts = startDate.split('/');
            if (parts.length === 3) {
              // Assuming format is MM/DD/YY or DD/MM/YY
              let day, month, year;

              // Try to determine the format based on values
              if (parseInt(parts[0]) > 12) {
                // First part is likely the day
                day = parseInt(parts[0]);
                month = parseInt(parts[1]);
              } else {
                // First part could be month or day, assume MM/DD format
                month = parseInt(parts[0]);
                day = parseInt(parts[1]);
              }

              year = parseInt(parts[2]);
              // Handle 2-digit years
              if (year < 100) {
                year += year < 50 ? 2000 : 1900;
              }

              // Use UTC to avoid timezone issues
              const dateObj = new Date(Date.UTC(year, month - 1, day));
              if (!isNaN(dateObj.getTime())) {
                startDate = dateObj.toISOString().split('T')[0]; // Format as YYYY-MM-DD
              }
            }
          }
        }

        const priorite = item[columnMapping['Priorité']];

        // Get TempsCycleTotal, which might be named differently
        const tempsCycleTotalField = columnMapping['TempsCycleTotal'];
        const tempsCycleTotal = parseFloat(item[tempsCycleTotalField]);



        return {
          id,
          codeProduit,
          quantiteDemandee,
          deadline,
          priorite,
          quantiteeOuverte,
          startDate,
          tempsCycleTotal
        };
      });

      // Clean up the uploaded file
      fs.unlinkSync(filePath);

      // Return data to frontend (will be stored in localStorage only)
      res.json(formattedData);
    } catch (readError) {
      return res.status(400).json({ message: 'Error reading Excel file. Make sure it is a valid Excel file.', error: readError.message });
    }
  } catch (error) {
    res.status(500).json({ message: 'Error processing Excel file', error: error.message });
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});