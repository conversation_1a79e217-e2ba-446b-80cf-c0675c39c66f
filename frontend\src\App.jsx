import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import './App.css';
// Note: hasUnsavedChanges functionality is now handled at the component level

// Components
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import LocationTracker from './components/LocationTracker';
import DataPreloader from './components/DataPreloader';
import Home from './pages/Home';
import FluxProduits from './pages/FluxProduits';
import Machines from './pages/Machines';
import Transit from './pages/Transit';
import RessourcesHumaines from './pages/RessourcesHumaines';
import Polyvalence from './pages/Polyvalence';
import RessourcesMaterielles from './pages/RessourcesMaterielles';
import StockMP from './pages/StockMP';
import PlanningResults from './pages/PlanningResults';

// Context Providers
import { LanguageProvider } from './components/contexts/LanguageContext';

// Create a modern dark theme
const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#3a86ff', // New blue
      light: '#5a9aff',
      dark: '#2667cc',
      contrastText: '#ffffff',
    },
    secondary: {
      main: '#ec4899', // Pink
      light: '#f472b6',
      dark: '#db2777',
      contrastText: '#ffffff',
    },
    background: {
      default: '#020617', // Even darker than before
      paper: '#0f172a', // Darker paper background
    },
    error: {
      main: '#ef4444', // Red
    },
    warning: {
      main: '#f59e0b', // Amber
    },
    info: {
      main: '#3b82f6', // Blue
    },
    success: {
      main: '#10b981', // Emerald
    },
    text: {
      primary: '#f8fafc', // Slate 50
      secondary: '#cbd5e1', // Slate 300
      disabled: '#64748b', // Slate 500
    },
    divider: 'rgba(148, 163, 184, 0.12)', // Slate 400 with opacity
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
    },
    h2: {
      fontWeight: 700,
    },
    h3: {
      fontWeight: 600,
    },
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 600,
    },
    h6: {
      fontWeight: 600,
    },
    button: {
      fontWeight: 600,
      textTransform: 'none',
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          padding: '10px 16px',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
          },
        },
        contained: {
          '&:hover': {
            transform: 'translateY(-2px)',
            transition: 'transform 0.2s ease-in-out',
          },
        },
      },
    },
    MuiContainer: {
      styleOverrides: {
        root: {
          maxWidth: '100% !important',
          width: '100%',
          paddingLeft: '0px',
          paddingRight: '0px',
          margin: '0 auto',
          boxSizing: 'border-box',
          '@media (max-width: 600px)': {
            paddingLeft: '8px',
            paddingRight: '8px',
          },
          '@media (min-width: 600px) and (max-width: 768px)': {
            paddingLeft: '12px',
            paddingRight: '12px',
          },
          '@media (min-width: 768px) and (max-width: 992px)': {
            paddingLeft: '14px',
            paddingRight: '14px',
          },
          '@media (min-width: 992px)': {
            paddingLeft: '16px',
            paddingRight: '16px',
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: '1px solid rgba(99, 102, 241, 0.1)',
          padding: '12px 16px',
          boxSizing: 'border-box',
          '@media (max-width: 600px)': {
            padding: '8px 12px',
            fontSize: '0.8rem',
          },
          '@media (min-width: 600px) and (max-width: 768px)': {
            padding: '10px 14px',
            fontSize: '0.85rem',
          },
          // Remove global text truncation - let individual components handle this
        },
        head: {
          fontWeight: 600,
          backgroundColor: 'rgba(15, 23, 42, 0.9)',
          color: '#f8fafc',
          position: 'sticky',
          top: 0,
          zIndex: 10,
          whiteSpace: 'normal', // Allow text wrapping in headers
          overflow: 'visible', // Make sure header text is fully visible
          lineHeight: 1.4,
          fontSize: '0.9rem',
          padding: '12px 16px',
          borderBottom: '2px solid rgba(99, 102, 241, 0.3)',
          '@media (max-width: 600px)': {
            padding: '8px 12px',
            fontSize: '0.8rem',
            lineHeight: 1.3,
          },
          '@media (min-width: 600px) and (max-width: 768px)': {
            padding: '10px 14px',
            fontSize: '0.85rem',
          },
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:hover': {
            backgroundColor: 'rgba(15, 23, 42, 0.8)',
          },
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1)',
          backgroundImage: 'linear-gradient(to right, #0f172a, #020617)',
        },
      },
    },
  },
});

function App() {
  // Note: beforeunload handling is now done at the component level where unsaved changes are tracked

  // Create a wrapper component to use the useLocation hook
  const AppContent = () => {
    return (
      <div className="app">
        <DataPreloader />
        <LocationTracker />
        <Navbar />
        <main className="content">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/flux-produits" element={<FluxProduits />} />
            <Route path="/machines" element={<Machines />} />
            <Route path="/transit" element={<Transit />} />
            <Route path="/ressources-humaines" element={<RessourcesHumaines />} />
            <Route path="/polyvalence" element={<Polyvalence />} />
            <Route path="/ressources-materielles" element={<RessourcesMaterielles />} />
            <Route path="/stock-mp" element={<StockMP />} />
            <Route path="/planning-results" element={<PlanningResults />} />
          </Routes>
        </main>
        <Footer />
      </div>
    );
  };

  return (
    <LanguageProvider>
      <ThemeProvider theme={darkTheme}>
        <CssBaseline />
        <Router>
          <AppContent />
        </Router>
      </ThemeProvider>
    </LanguageProvider>
  );
}

export default App
