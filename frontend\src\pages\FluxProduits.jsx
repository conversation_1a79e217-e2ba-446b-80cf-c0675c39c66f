import React, { useState, useEffect } from 'react';
import { Container, Typography, Alert, Snackbar, Box, Paper, Divider } from '@mui/material';
import DataTable from '../components/DataTable';
import UnsavedChangesDialog from '../components/UnsavedChangesDialog';
import BackButton from '../components/BackButton';
import { fluxProduits, saveAllData, checkUnsavedChanges } from '../services/navbarApi';

const FluxProduits = () => {
  const [fluxProduitsData, setFluxProduitsData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);

  const columns = [
    { field: 'codeProduit', headerName: 'Code Produit', type: 'string', width: '120px' },
    { field: 'ordre', headerName: 'Ordre', type: 'number', width: '80px' },
    { field: 'poste', headerName: 'Poste', type: 'string', width: '120px' },
    { field: 'machinesPossibles', headerName: 'Machines Possibles', type: 'string', width: '350px' },
    { field: 'tempsCycle', headerName: 'Temps Cycle (min)', type: 'number', width: '140px' },
    { field: 'composant', headerName: 'Composant', type: 'string', width: '200px' },
  ];

  useEffect(() => {
    fetchFluxProduits(false); // Use cached data if available for faster loading
  }, []);

  // Add beforeunload event listener to show browser's native "unsaved changes" alert
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      if (checkUnsavedChanges()) {
        event.preventDefault();
        event.returnValue = ''; // This triggers the browser's native dialog
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const fetchFluxProduits = async (forceRefresh = false) => {
    try {
      setLoading(true);
      const data = await fluxProduits.getAll(forceRefresh); // Only force refresh when explicitly requested
      setFluxProduitsData(data);
      setError('');
    } catch (error) {
      setError('Erreur lors du chargement des flux produits');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = async (newItem) => {
    try {
      // Add an ID if not present
      if (!newItem.id) {
        newItem.id = Date.now().toString();
      }

      // Convert numeric fields
      newItem.ordre = parseInt(newItem.ordre);
      newItem.tempsCycle = parseFloat(newItem.tempsCycle);

      const addedItem = await fluxProduits.add(newItem);
      // Refresh data from the API to ensure we have the latest state
      const updatedData = await fluxProduits.getAll();
      setFluxProduitsData(updatedData);
      showNotification('Flux produit ajouté avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de l\'ajout du flux produit', 'error');
    }
  };

  const handleEdit = async (updatedItem) => {
    try {
      // Convert numeric fields
      updatedItem.ordre = parseInt(updatedItem.ordre);
      updatedItem.tempsCycle = parseFloat(updatedItem.tempsCycle);

      const editedItem = await fluxProduits.update(updatedItem);
      setFluxProduitsData(fluxProduitsData.map(item => item.id === editedItem.id ? editedItem : item));
      showNotification('Flux produit mis à jour avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la mise à jour du flux produit', 'error');
    }
  };

  const handleDelete = async (id) => {
    try {
      await fluxProduits.delete(id);
      setFluxProduitsData(fluxProduitsData.filter(item => item.id !== id));
      showNotification('Flux produit supprimé avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la suppression du flux produit', 'error');
    }
  };

  const handleSave = async () => {
    try {
      // Save all data to backend
      const result = await saveAllData();

      // Always show success message since we've modified saveAllData to always return success: true
      // when the data is saved to localStorage, even if there were backend errors
      showNotification(result.message || 'Changements sauvegardés avec succès', 'success');
    } catch (error) {
      showNotification('Erreur lors de la sauvegarde des changements', 'error');
    }
  };



  // Handle dialog confirmation (OK button)
  const handleConfirmNavigation = () => {
    setShowUnsavedDialog(false);
    if (pendingNavigation) {
      window.location.href = pendingNavigation;
    }
  };

  // Handle dialog cancellation
  const handleCancelNavigation = () => {
    setShowUnsavedDialog(false);
    setPendingNavigation(null);
  };

  const showNotification = (message, severity) => {
    setNotification({ open: true, message, severity });
  };

  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  return (
    <Container sx={{ backgroundColor: 'transparent', maxWidth: '100%', padding: 0 }}>
      <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
        <BackButton />
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Paper elevation={3} sx={{ p: 2, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="h2">
            Flux Produits
          </Typography>
        </Box>

        <Divider sx={{ mb: 2 }} />

        <DataTable
          title=""
          columns={columns}
          data={fluxProduitsData}
          onAdd={handleAdd}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onSave={handleSave}
          loading={loading}
        />
      </Paper>

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity}>
          {notification.message}
        </Alert>
      </Snackbar>

      {/* Unsaved Changes Dialog */}
      <UnsavedChangesDialog
        open={showUnsavedDialog}
        onCancel={handleCancelNavigation}
        onConfirm={handleConfirmNavigation}
      />
    </Container>
  );
};

export default FluxProduits;
