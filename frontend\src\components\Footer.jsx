import React from 'react';
import { useLanguage } from './contexts/LanguageContext';
import { useTheme, Box, Typography, Link as MuiLink, Container } from '@mui/material';
import { Link } from 'react-router-dom';

const Footer = () => {
  const { language } = useLanguage();
  const theme = useTheme();
  const isRTL = language === 'ar';
  const currentYear = new Date().getFullYear();

  return (
    <Box
      component="footer"
      className="fade-in"
      sx={{
        width: '100%',
        padding: '1rem 0',
        background: 'linear-gradient(180deg, rgba(30, 41, 59, 0.8) 0%, rgba(15, 23, 42, 0.9) 100%)',
        backdropFilter: 'blur(10px)',
        color: theme.palette.text.secondary,
        textAlign: 'center',
        fontSize: '0.85rem',
        position: 'fixed',
        bottom: 0,
        left: 0,
        userSelect: 'none',
        borderTop: `1px solid ${theme.palette.divider}`,
        boxShadow: '0 -10px 15px -3px rgba(0, 0, 0, 0.1), 0 -4px 6px -2px rgba(0, 0, 0, 0.05)',
        zIndex: 100,
        direction: isRTL ? 'rtl' : 'ltr',
      }}
    >
      <Container maxWidth="lg">
        <Box
          sx={{
            display: 'flex',
            flexWrap: 'wrap',
            justifyContent: 'center',
            alignItems: 'center',
            gap: { xs: 1, sm: 2 },
          }}
        >
          <Typography
            variant="body2"
            sx={{
              opacity: 0.8,
              fontWeight: 500,
            }}
          >
            &copy; {currentYear} Planification Michaud
          </Typography>

          <Box
            sx={{
              width: '4px',
              height: '4px',
              borderRadius: '50%',
              backgroundColor: theme.palette.primary.main,
              opacity: 0.6,
              display: { xs: 'none', sm: 'block' }
            }}
          />

          <MuiLink
            href="http://linkedin.com/in/fourat-selmi-92634a229"
            target="_blank"
            rel="noopener noreferrer"
            sx={{
              color: theme.palette.primary.main,
              textDecoration: 'none',
              transition: 'all 0.3s ease',
              fontWeight: 500,
              position: 'relative',
              '&:hover': {
                color: theme.palette.primary.light,
                transform: 'translateY(-2px)',
              },
              '&::after': {
                content: '""',
                position: 'absolute',
                width: '0%',
                height: '2px',
                bottom: -2,
                left: '50%',
                transform: 'translateX(-50%)',
                backgroundColor: theme.palette.primary.main,
                transition: 'width 0.3s ease',
                borderRadius: '2px',
              },
              '&:hover::after': {
                width: '100%',
              },
              display: 'inline-flex',
            }}
          >
            Selmi Fourat
          </MuiLink>

          <Box
            sx={{
              width: '4px',
              height: '4px',
              borderRadius: '50%',
              backgroundColor: theme.palette.primary.main,
              opacity: 0.6,
              display: { xs: 'none', sm: 'block' }
            }}
          />

          <Typography
            variant="body2"
            sx={{
              opacity: 0.8,
              fontWeight: 500,
            }}
          >
            All Rights Reserved
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
