import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import UnsavedChangesDialog from './UnsavedChangesDialog';
import { checkUnsavedChanges, discardChanges } from '../services/navbarApi';

/**
 * A custom Link component that checks for unsaved changes before navigating
 */
const NavigationLinkWithCheck = ({ to, children, ...props }) => {
  const [showDialog, setShowDialog] = useState(false);
  const [pendingPath, setPendingPath] = useState(null);

  const handleClick = (e) => {
    // Check if there are unsaved changes
    if (checkUnsavedChanges()) {
      e.preventDefault();
      setPendingPath(to);
      setShowDialog(true);
    }
    // If no unsaved changes, the link will work normally
  };

  const handleConfirm = () => {
    setShowDialog(false);
    // Discard changes when user confirms navigation
    discardChanges();
    if (pendingPath) {
      window.location.href = pendingPath;
    }
  };

  const handleCancel = () => {
    setShowDialog(false);
    setPendingPath(null);
  };

  return (
    <>
      <Link to={to} onClick={handleClick} {...props}>
        {children}
      </Link>

      <UnsavedChangesDialog
        open={showDialog}
        onCancel={handleCancel}
        onConfirm={handleConfirm}
      />
    </>
  );
};

export default NavigationLinkWithCheck;
