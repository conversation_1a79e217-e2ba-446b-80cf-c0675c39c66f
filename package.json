{"name": "planification-mi<PERSON><PERSON>", "version": "1.0.0", "description": "Application de planification de production pour Michaud", "scripts": {"install:all": "npm run install:backend && npm run install:frontend", "install:backend": "cd backend && npm install", "install:frontend": "cd frontend && npm install", "start": "npm-run-all --parallel start:backend start:frontend", "start:backend": "cd backend && npm run dev", "start:frontend": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "clean": "rimraf node_modules backend/node_modules frontend/node_modules"}, "keywords": ["planification", "production", "react", "express"], "author": "", "license": "ISC", "devDependencies": {"npm-run-all": "^4.1.5", "rimraf": "^5.0.5"}, "dependencies": {"axios": "^1.9.0", "recharts": "^2.15.3", "xlsx": "^0.18.5"}}