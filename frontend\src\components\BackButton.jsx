import { useState } from 'react';
import { Button } from '@mui/material';
import NavigateBeforeIcon from '@mui/icons-material/NavigateBefore';
import { useNavigate, useLocation } from 'react-router-dom';
import { checkUnsavedChanges, discardChanges } from '../services/navbarApi';
import UnsavedChangesDialog from './UnsavedChangesDialog';

// List of navbar table paths
const navbarTablePaths = [
  '/flux-produits',
  '/machines',
  '/transit',
  '/ressources-humaines',
  '/polyvalence',
  '/ressources-materielles',
  '/stock-mp',
];

// List of allowed previous pages for navbar tables
const allowedPreviousPages = [
  '/', // Home
  '/planning-results', // Résultats de Planification (Excel or Manual)
];

/**
 * A reusable back button component that navigates to the previous page
 * and checks for unsaved changes before navigating.
 * If the user came from the home page, it will navigate back to home
 * instead of cycling between navbar tables.
 */
const BackButton = ({ onNavigate }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [showUnsavedDialog, setShowUnsavedDialog] = useState(false);
  const [pendingNavigation, setPendingNavigation] = useState(null);

  // Get the previous path and entry method from sessionStorage
  const getPreviousPath = () => {
    return sessionStorage.getItem('previousPath') || '/';
  };

  // Get the previous entry method from sessionStorage
  const getPreviousEntryMethod = () => {
    return sessionStorage.getItem('previousEntryMethod');
  };

  const handleGoBack = () => {
    // If there's a custom navigation handler, use it
    if (onNavigate) {
      onNavigate();
      return;
    }

    // If we're on a navbar table page
    if (navbarTablePaths.includes(location.pathname)) {
      // Get the previous path from sessionStorage
      const previousPath = getPreviousPath();

      // Default to home if no previous path is stored or if previous path is not allowed
      const targetPath = allowedPreviousPages.includes(previousPath) ? previousPath : '/';

      // If the target path is the planning-results page, we need to include the entry method
      let navigationOptions = {};
      if (targetPath === '/planning-results') {
        const entryMethod = getPreviousEntryMethod();
        if (entryMethod) {
          navigationOptions = { state: { entryMethod } };
          console.log('Navigating back to planning-results with entry method:', entryMethod);
        }
      }

      // Check for unsaved changes before navigating
      if (checkUnsavedChanges()) {
        // Show confirmation dialog before navigating
        setPendingNavigation({ path: targetPath, options: navigationOptions });
        setShowUnsavedDialog(true);
      } else {
        // Navigate to the target path with options if needed
        navigate(targetPath, navigationOptions);
      }
    } else {
      // For non-navbar table pages, use the standard back behavior
      if (checkUnsavedChanges()) {
        // Let the browser's history.back() handle the unsaved changes dialog
        window.history.back();
      } else {
        // If no unsaved changes, navigate back
        navigate(-1);
      }
    }
  };

  // Handle dialog confirmation (OK button)
  const handleConfirmNavigation = () => {
    setShowUnsavedDialog(false);
    // Discard changes when user confirms navigation
    discardChanges();
    if (pendingNavigation) {
      if (typeof pendingNavigation === 'object' && pendingNavigation.path) {
        // If pendingNavigation is an object with path and options
        navigate(pendingNavigation.path, pendingNavigation.options || {});
      } else {
        // If pendingNavigation is just a string path
        navigate(pendingNavigation);
      }
    }
  };

  // Handle dialog cancellation
  const handleCancelNavigation = () => {
    setShowUnsavedDialog(false);
    setPendingNavigation(null);
  };

  return (
    <>
      <Button
        variant="outlined"
        color="primary"
        onClick={handleGoBack}
        startIcon={<NavigateBeforeIcon />}
        sx={{
          mb: 2,
          mt: 2,
          borderRadius: '8px',
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: '0 4px 8px rgba(99, 102, 241, 0.2)',
          },
          transition: 'all 0.2s ease'
        }}
      >
        Précédent
      </Button>

      {/* Unsaved Changes Dialog */}
      <UnsavedChangesDialog
        open={showUnsavedDialog}
        onCancel={handleCancelNavigation}
        onConfirm={handleConfirmNavigation}
      />
    </>
  );
};

export default BackButton;
