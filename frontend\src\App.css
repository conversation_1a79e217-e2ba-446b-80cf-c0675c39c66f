* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

#root {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  text-align: center;
}


.app {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #020617; /* Even darker background */
  background-image:
    radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(236, 72, 153, 0.02) 0%, transparent 50%);
}

.content {
  flex: 1;
  padding: 28px 16px 80px; /* Top, horizontal, bottom padding */
  width: 100%;
  overflow-x: hidden;
  animation: fadeIn 0.5s ease-in-out;
  box-sizing: border-box;
}

.table-container {
  margin-top: 24px;
  width: 100%;
  min-width: 100%;
  overflow: hidden; /* Hide overflow at container level */
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  background-color: rgba(15, 23, 42, 0.7); /* Darker background with opacity */
  backdrop-filter: blur(8px);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(99, 102, 241, 0.1);
  padding-bottom: 16px;
  box-sizing: border-box;
}

.table-container:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px);
  border: 1px solid rgba(99, 102, 241, 0.15);
}

.table-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

/* Button containers for tables */

.table-buttons-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  margin-bottom: 24px;
  padding: 0 24px;
  width: 100%;
  box-sizing: border-box;
}

.button-left-space {
  flex: 1;
  display: flex;
  justify-content: flex-start;
}

.button-center {
  flex: 2;
  display: flex;
  justify-content: center;
}

.button-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.table-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  padding: 16px;
  border-top: 1px solid rgba(148, 163, 184, 0.12);
}

.form-container {
  width: 100%;
  margin: 0 auto;
  padding: 24px;
  border-radius: 12px;
  background-color: rgba(15, 23, 42, 0.7);
  backdrop-filter: blur(8px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(99, 102, 241, 0.1);
}

.home-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80vh;
  padding: 24px;
}

.input-method-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  margin-top: 32px;
  width: 100%;
  padding: 16px;
  border-radius: 12px;
  background-color: rgba(3, 7, 18, 0.7);
  backdrop-filter: blur(4px);
  transition: all 0.3s ease;
  border: 1px solid rgba(99, 102, 241, 0.1);
}

.input-method-container:hover {
  background-color: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(99, 102, 241, 0.15);
}

.planning-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.5);
  border-radius: 4px;
  border: 1px solid rgba(15, 23, 42, 0.3);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.7);
  cursor: pointer;
}

::-webkit-scrollbar-corner {
  background: transparent;
}

/* Specific styling for table container scrollbars */
.MuiTableContainer-root::-webkit-scrollbar {
  height: 8px;
  width: 8px;
}

.MuiTableContainer-root::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
  border-radius: 4px;
}

.MuiTableContainer-root::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.6);
  border-radius: 4px;
  border: 1px solid rgba(15, 23, 42, 0.4);
}

.MuiTableContainer-root::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.8);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

/* Card hover effects */
.hover-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Button animations */
.btn-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

/* Logo styles */
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* ===== RESPONSIVE DESIGN MEDIA QUERIES ===== */

/* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {
  .content {
    padding: 16px 8px 60px;
  }

  .table-container {
    margin-top: 16px;
    border-radius: 8px;
  }

  .table-buttons-container {
    flex-direction: column;
    gap: 12px;
    padding: 0 16px;
    align-items: stretch;
  }

  .button-left-space,
  .button-center,
  .button-right {
    flex: none;
    width: 100%;
    justify-content: center;
  }

  .table-actions {
    flex-direction: column;
    gap: 12px;
    margin-bottom: 16px;
  }

  .home-container {
    padding: 16px;
    min-height: 70vh;
  }

  .input-method-container {
    margin-top: 24px;
    padding: 12px;
  }

  .planning-container {
    gap: 24px;
  }
}

/* Small devices (portrait tablets and large phones, 600px to 768px) */
@media only screen and (min-width: 600px) and (max-width: 768px) {
  .content {
    padding: 20px 12px 70px;
  }

  .table-container {
    margin-top: 20px;
  }

  .table-buttons-container {
    padding: 0 20px;
    flex-wrap: wrap;
    gap: 8px;
  }

  .button-center {
    flex: 1;
    min-width: 200px;
  }

  .home-container {
    padding: 20px;
  }

  .input-method-container {
    margin-top: 28px;
    padding: 14px;
  }
}

/* Medium devices (landscape tablets, 768px to 992px) */
@media only screen and (min-width: 768px) and (max-width: 992px) {
  .content {
    padding: 24px 14px 75px;
  }

  .table-container {
    margin-top: 22px;
  }

  .table-buttons-container {
    padding: 0 22px;
  }

  .home-container {
    padding: 22px;
  }

  .input-method-container {
    margin-top: 30px;
    padding: 15px;
  }
}

/* Large devices (laptops/desktops, 992px to 1200px) */
@media only screen and (min-width: 992px) and (max-width: 1200px) {
  .content {
    padding: 26px 15px 78px;
  }

  .table-container {
    margin-top: 23px;
  }

  .table-buttons-container {
    padding: 0 23px;
  }
}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {
  .content {
    padding: 28px 16px 80px;
  }

  .table-container {
    margin-top: 24px;
  }

  .table-buttons-container {
    padding: 0 24px;
  }
}


