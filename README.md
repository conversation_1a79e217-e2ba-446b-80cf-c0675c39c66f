# Planification Michaud

Application de planification de production pour Michaud.

## Structure du projet

- `frontend/` : Application React + Vite
- `backend/` : Serveur Node.js + Express

## Prérequis

- Node.js (version 18 ou supérieure)
- npm (version 8 ou supérieure)
- Un navigateur web moderne (Chrome, Firefox, Edge)

## Installation

### Méthode recommandée (automatique)

1. Décompressez le fichier ZIP dans un dossier de votre choix
2. Exécutez le fichier `install-and-start.bat` qui va:
   - Vérifier que Node.js est installé
   - Créer le répertoire de données si nécessaire
   - Installer les dépendances du backend et du frontend
   - Démarrer les serveurs backend et frontend

### Méthode manuelle

Si vous préférez installer manuellement:

1. Décompressez le fichier ZIP dans un dossier de votre choix
2. Ouvrez une invite de commande dans ce dossier
3. <PERSON><PERSON><PERSON> le répertoire de données: `mkdir backend\data`
4. Installez les dépendances du backend: `cd backend && npm install`
5. Installez les dépendances du frontend: `cd frontend && npm install`
6. Démarrez le backend: `cd backend && npm run dev`
7. Dans une nouvelle invite de commande, démarrez le frontend: `cd frontend && npm run dev`

## Démarrage (après installation)

Exécutez le fichier `start-app.bat` pour démarrer l'application, ou démarrez manuellement:

- Backend: `cd backend && npm run dev`
- Frontend: `cd frontend && npm run dev`

Accédez ensuite à l'application dans votre navigateur:
- Frontend: http://localhost:5173
- Backend: http://localhost:5000/api

## Stockage des données

L'application stocke les données principalement dans le backend:

1. **Backend**: Les données sont sauvegardées dans des fichiers JSON dans le dossier `backend/data/`. C'est la source principale de données.
2. **LocalStorage**: Les données sont également mises en cache dans le localStorage du navigateur, mais uniquement comme sauvegarde temporaire.

Lorsque vous exécutez l'application sur un nouvel ordinateur, elle chargera toujours les données depuis le backend. Le localStorage n'est utilisé que comme cache temporaire et en dernier recours si le backend n'est pas accessible. L'application n'utilise jamais de données fictives (mock data).

## Résolution des problèmes

### Problèmes de données

Si vous rencontrez des problèmes avec les données:

1. **Vérifiez le backend** : Assurez-vous que le backend est en cours d'exécution (`cd backend && npm run dev`).
2. **Vérifiez les fichiers de données** : Les fichiers JSON doivent exister dans le dossier `backend/data/`.
3. **Données automatiquement créées** : Si les fichiers n'existent pas, ils seront créés automatiquement au démarrage du backend avec des structures vides.
4. **Cache localStorage** : L'application charge maintenant automatiquement les données depuis le backend. Le localStorage est utilisé uniquement comme cache.
5. **Effacer le cache** : Utilisez le script `clear-localStorage.bat` pour effacer le cache si nécessaire.
6. **Redémarrage complet** : Redémarrez l'application complète en utilisant le script `install-and-start.bat` si les problèmes persistent.

**Important** : L'application priorise maintenant toujours les données du backend par rapport au cache local. Cela garantit que les données réelles sont toujours chargées lors de l'installation sur un nouvel ordinateur.

### Problèmes de connexion au backend

Si l'application ne parvient pas à se connecter au backend:

1. Vérifiez que le serveur backend est en cours d'exécution (terminal avec `npm run dev`).
2. Assurez-vous que le port 5000 n'est pas utilisé par une autre application.
3. Vérifiez qu'aucun pare-feu ne bloque les connexions sur le port 5000.

### Problèmes d'installation

Si vous rencontrez des erreurs lors de l'installation:

1. Vérifiez que Node.js est correctement installé: `node --version` et `npm --version`.
2. Essayez de supprimer les dossiers `node_modules` et les fichiers `package-lock.json`, puis réinstallez les dépendances.
3. Si vous utilisez un proxy d'entreprise, configurez npm pour utiliser ce proxy: `npm config set proxy http://proxy-url:port`.

### Problèmes de fonctionnalités

Si certaines fonctionnalités ne fonctionnent pas correctement:

1. Vérifiez la console du navigateur (F12) pour voir s'il y a des erreurs.
2. Assurez-vous que toutes les dépendances sont correctement installées.
3. Essayez de vider le cache du navigateur ou d'utiliser une fenêtre de navigation privée.

## Format du fichier Excel

Pour l'importation des données, le fichier Excel doit contenir une feuille nommée "Produits" avec les colonnes suivantes :

| Nom de colonne | Description | Exemple |
|----------------|-------------|---------|
| Code Produit | Code du produit | P001 |
| Quantité Demandée | Quantité demandée | 100 |
| Date Limite | Date limite | 2023-12-31 |
| Priorité | Priorité (Haute, Moyenne, Faible) | Haute |
| Date Début | Date de début | 2023-11-01 |
| Temps Cycle Total | Temps de cycle total en minutes | 120 |

Les noms de colonnes alternatifs sont également pris en charge :
- Code Produit : CodeProduit, Code
- Quantité Demandée : QuantiteDemandee, Quantite
- Date Limite : DateLimite, Deadline
- Priorité : Priorite, Priority
- Date Début : DateDebut, Start Date
- Temps Cycle Total : TempsCycleTotal, Temps

Si la feuille nommée 'Produits' n'est pas trouvée, la première feuille du classeur sera utilisée.

### Structure Excel d'exemple

```
| Code Produit | Quantité Demandée | Date Limite | Priorité | Date Début | Temps Cycle Total |
|--------------|-------------------|-------------|----------|------------|-------------------|
| P001         | 100               | 2023-12-31  | Haute    | 2023-11-01 | 120               |
| P002         | 200               | 2023-12-15  | Moyenne  | 2023-11-05 | 90                |
| P003         | 150               | 2023-12-20  | Faible   | 2023-11-10 | 60                |
```

Vous pouvez créer ce fichier Excel en utilisant Microsoft Excel, Google Sheets ou tout autre logiciel de tableur capable d'exporter au format .xlsx.

## Tables de données

### Flux Produits
- Code Produit (string)
- Ordre (int)
- Poste (string)
- Machines Possibles (string)
- Temps Cycle (min) (float)
- Composant (string)

### Machines
- Nom Machine (string)
- Poste (string)
- Capacité (int)
- Temps Setup (min) (int)
- Disponibilité (int)

### Transit
- Machine Source (string)
- Machine Destination (string)
- Temps Transit (min) (int)

### Ressources Humaines
- Nom (string)
- Disponible (string - "Oui" ou "Non")

### Polyvalence
- Opérateur (string)
- Heures Par Jour (int)
- Machine BA 600 (int)
- Machine graissage 3 (int)
- Machine soudage 2 (int)
- Machine marquage 2 (int)
- Machine emballage 2 (int)
- Machine BA 500 (int)
- Machine graissage (int)
- Machine soudage (int)
- Machine marquage 1 (int)
- Machine emballage 1 (int)

### Ressources Matérielles
- CodeProduit (string)
- Composant (int)
- Designation (string)
- Qté Nette (float)
- Perte Cont. (float)
- Perte Tech. (float)
- Qté Brut (float)
- SEUIL (float)
- Unité (string)
- MOQ (float)
- Délai d'appro (jours calendaires) (int)
